<?php
// TaskQueue.php - 任务队列管理类

class TaskQueue {
    private $conn;

    public function __construct($dbConnection) {
        $this->conn = $dbConnection;
    }

    /**
     * 生成唯一的任务ID
     */
    public function generateTaskId() {
        return uniqid('task_', true) . '_' . time();
    }

    /**
     * 创建新任务
     */
    public function createTask($taskData) {
        $taskId = $this->generateTaskId();

        try {
            $this->conn->begin_transaction();

            // 准备插入语句
            $stmt = $this->conn->prepare("INSERT INTO create_promotion_link (
                task_id, admin_account_name, account_id, project, appid,
                book_id, chapter_num, name, media_id,
                postback_rule_id, recharge_panel_id, advertiser_account_id,
                status, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending', NOW())");

            foreach ($taskData as $data) {
                $stmt->bind_param(
                    "sssissisiiss",
                    $taskId,
                    $data['admin_account_name'],
                    $data['account_id'],
                    $data['project'],
                    $data['appid'],
                    $data['book_id'],
                    $data['chapter_num'],
                    $data['name'],
                    $data['media_id'],
                    $data['postback_rule_id'],
                    $data['recharge_panel_id'],
                    $data['advertiser_account_id']
                );

                if (!$stmt->execute()) {
                    throw new Exception("任务创建失败: " . $stmt->error);
                }
            }

            $this->conn->commit();
            return $taskId;

        } catch (Exception $e) {
            $this->conn->rollback();
            throw $e;
        }
    }

    /**
     * 获取下一个待处理的任务
     */
    public function getNextPendingTask() {
        $stmt = $this->conn->prepare("
            SELECT task_id, COUNT(*) as task_count
            FROM create_promotion_link
            WHERE status = 'pending'
            GROUP BY task_id
            ORDER BY MIN(created_at) ASC
            LIMIT 1
        ");

        $stmt->execute();
        $result = $stmt->get_result();

        if ($row = $result->fetch_assoc()) {
            return $row['task_id'];
        }

        return null;
    }

    /**
     * 更新任务状态
     */
    public function updateTaskStatus($taskId, $status, $errorMessage = null) {
        $stmt = $this->conn->prepare("
            UPDATE create_promotion_link
            SET status = ?, error_message = ?, updated_at = NOW()
            WHERE task_id = ?
        ");

        $stmt->bind_param("sss", $status, $errorMessage, $taskId);
        return $stmt->execute();
    }

    /**
     * 获取任务详情
     */
    public function getTaskDetails($taskId) {
        $stmt = $this->conn->prepare("
            SELECT * FROM create_promotion_link
            WHERE task_id = ?
            ORDER BY created_at ASC
        ");

        $stmt->bind_param("s", $taskId);
        $stmt->execute();
        $result = $stmt->get_result();

        $tasks = [];
        while ($row = $result->fetch_assoc()) {
            $tasks[] = $row;
        }

        return $tasks;
    }

    /**
     * 获取任务状态
     */
    public function getTaskStatus($taskId) {
        $stmt = $this->conn->prepare("
            SELECT status, COUNT(*) as total_count,
                   SUM(CASE WHEN status = 'done' THEN 1 ELSE 0 END) as done_count,
                   SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed_count,
                   MIN(created_at) as created_at,
                   MAX(updated_at) as updated_at
            FROM create_promotion_link
            WHERE task_id = ?
            GROUP BY task_id
        ");

        $stmt->bind_param("s", $taskId);
        $stmt->execute();
        $result = $stmt->get_result();

        return $result->fetch_assoc();
    }

    /**
     * 获取任务的推广链接结果
     */
    public function getTaskResults($taskId) {
        $stmt = $this->conn->prepare("
            SELECT l.*, l.name as link_name, l.advertiser_account_id
            FROM link l
            WHERE l.task_id = ?
            ORDER BY l.created_at ASC
        ");

        $stmt->bind_param("s", $taskId);
        $stmt->execute();
        $result = $stmt->get_result();

        $results = [];
        while ($row = $result->fetch_assoc()) {
            $results[] = $row;
        }

        return $results;
    }

    /**
     * 检查是否有正在运行的任务
     */
    public function hasRunningTask() {
        $stmt = $this->conn->prepare("
            SELECT COUNT(*) as count
            FROM create_promotion_link
            WHERE status = 'running'
        ");

        $stmt->execute();
        $result = $stmt->get_result();
        $row = $result->fetch_assoc();

        return $row['count'] > 0;
    }
}
