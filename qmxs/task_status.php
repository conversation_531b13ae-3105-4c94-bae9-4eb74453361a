<?php
// task_status.php - 任务状态查询页面
require_once 'config.php';
require_once 'classes/TaskQueue.php';

// 设置内部编码和页面编码
mb_internal_encoding('UTF-8');
header('Content-Type: text/html; charset=UTF-8');

// 获取任务ID
$taskId = $_GET['task_id'] ?? '';

if (empty($taskId)) {
    die("错误：缺少任务ID参数");
}

// 连接数据库
$conn = new mysqli(DB_HOST, DB_USER, DB_PASSWORD, DB_NAME);
if ($conn->connect_error) {
    die("连接失败: " . $conn->connect_error);
}
$conn->set_charset("utf8mb4");

// 初始化任务队列
$taskQueue = new TaskQueue($conn);

// 处理下载请求
if (isset($_GET['action']) && $_GET['action'] === 'download') {
    $results = $taskQueue->getTaskResults($taskId);

    if (empty($results)) {
        die("没有找到任务结果或任务尚未完成");
    }

    // 生成Excel文件
    $filename = 'promotion_links_' . $taskId . '_' . date('Y-m-d_H-i-s') . '.xls';

    header('Content-Type: application/vnd.ms-excel');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: max-age=0');
    header('Pragma: public');

    // 输出Excel XML格式
    echo '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
    echo '<?mso-application progid="Excel.Sheet"?>' . "\n";
    echo '<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet"' . "\n";
    echo ' xmlns:o="urn:schemas-microsoft-com:office:office"' . "\n";
    echo ' xmlns:x="urn:schemas-microsoft-com:office:excel"' . "\n";
    echo ' xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet"' . "\n";
    echo ' xmlns:html="http://www.w3.org/TR/REC-html40">' . "\n";

    // 定义样式
    echo '<Styles>' . "\n";
    echo '<Style ss:ID="Header">' . "\n";
    echo '<Font ss:Bold="1"/>' . "\n";
    echo '<Interior ss:Color="#CCE5FF" ss:Pattern="Solid"/>' . "\n";
    echo '<Borders>' . "\n";
    echo '<Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>' . "\n";
    echo '<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>' . "\n";
    echo '<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>' . "\n";
    echo '<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>' . "\n";
    echo '</Borders>' . "\n";
    echo '</Style>' . "\n";
    echo '<Style ss:ID="Data">' . "\n";
    echo '<Borders>' . "\n";
    echo '<Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>' . "\n";
    echo '<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>' . "\n";
    echo '<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>' . "\n";
    echo '<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>' . "\n";
    echo '</Borders>' . "\n";
    echo '</Style>' . "\n";
    echo '</Styles>' . "\n";

    echo '<Worksheet ss:Name="推广链接">' . "\n";
    echo '<Table>' . "\n";

    // 设置列宽
    echo '<Column ss:Width="120"/>' . "\n"; // 账户ID(必填)
    echo '<Column ss:Width="100"/>' . "\n"; // 小小程序名称(必填)
    echo '<Column ss:Width="180"/>' . "\n"; // 小程序原始ID(必填)
    echo '<Column ss:Width="300"/>' . "\n"; // 小程序路径参数
    echo '<Column ss:Width="150"/>' . "\n"; // 资产备注信息（长度不超过15）
    echo '<Column ss:Width="150"/>' . "\n"; // 小程序标签（/分割，每个标签长度不超过6，最多6个）
    echo '<Column ss:Width="150"/>' . "\n"; // 引导文案（长度不超过14）
    echo '<Column ss:Width="150"/>' . "\n"; // 小程序简介（最大长度不超过50）

    // 表头
    echo '<Row>' . "\n";
    echo '<Cell ss:StyleID="Header"><Data ss:Type="String">账户ID(必填)</Data></Cell>' . "\n";
    echo '<Cell ss:StyleID="Header"><Data ss:Type="String">小程序名称(必填)</Data></Cell>' . "\n";
    echo '<Cell ss:StyleID="Header"><Data ss:Type="String">小程序原始ID(必填)</Data></Cell>' . "\n";
    echo '<Cell ss:StyleID="Header"><Data ss:Type="String">小程序路径参数</Data></Cell>' . "\n";
    echo '<Cell ss:StyleID="Header"><Data ss:Type="String">资产备注信息（长度不超过15）</Data></Cell>' . "\n";
    echo '<Cell ss:StyleID="Header"><Data ss:Type="String">小程序标签（/分割，每个标签长度不超过6，最多6个）</Data></Cell>' . "\n";
    echo '<Cell ss:StyleID="Header"><Data ss:Type="String">引导文案（长度不超过14）</Data></Cell>' . "\n";
    echo '<Cell ss:StyleID="Header"><Data ss:Type="String">小程序简介（最大长度不超过50）</Data></Cell>' . "\n";
    echo '</Row>' . "\n";

    // 数据行
    foreach ($results as $result) {
        $appName = $result['app_name'] ?: '七猫小说';
        $appId = $result['appid'] ?: 'wxe3a874175a6e6ed3';
        $advertiserId = $result['advertiser_account_id'] ?: '';
        $linkUrl = $result['link'] ?: '';
        $linkName = $result['link_name'] ?: '';

        echo '<Row>' . "\n";
        echo '<Cell ss:StyleID="Data"><Data ss:Type="String">' . htmlspecialchars($advertiserId) . '</Data></Cell>' . "\n";
        echo '<Cell ss:StyleID="Data"><Data ss:Type="String">' . htmlspecialchars($appName) . '</Data></Cell>' . "\n";
        echo '<Cell ss:StyleID="Data"><Data ss:Type="String">' . htmlspecialchars($appId) . '</Data></Cell>' . "\n";
        echo '<Cell ss:StyleID="Data"><Data ss:Type="String">' . htmlspecialchars($linkUrl) . '</Data></Cell>' . "\n";
        echo '<Cell ss:StyleID="Data"><Data ss:Type="String">' . htmlspecialchars($linkName) . '</Data></Cell>' . "\n";
        echo '</Row>' . "\n";
    }

    echo '</Table>' . "\n";
    echo '</Worksheet>' . "\n";
    echo '</Workbook>' . "\n";

    $conn->close();
    exit;
}

// 处理重新处理失败任务请求
if (isset($_GET['action']) && $_GET['action'] === 'retry') {
    // 首先将失败的任务重置为pending状态
    $stmt = $conn->prepare("UPDATE create_promotion_link SET status = 'pending', error_message = NULL, updated_at = NOW() WHERE task_id = ? AND status = 'failed'");
    $stmt->bind_param("s", $taskId);

    if ($stmt->execute() && $stmt->affected_rows > 0) {
        echo "<script>alert('已重置失败任务状态，正在重新处理...'); window.location.href='task_status.php?task_id=$taskId&action=process';</script>";
    } else {
        echo "<script>alert('重置任务状态失败或没有失败的任务需要重置'); window.location.href='task_status.php?task_id=$taskId';</script>";
    }
    exit;
}

// 处理启动任务处理请求
if (isset($_GET['action']) && $_GET['action'] === 'process') {
    // 调用任务处理器
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
    $baseUrl = $protocol . '://' . $host . dirname($_SERVER['REQUEST_URI']);
    $apiUrl = $baseUrl . '/modules/task_queue_processor.php';

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $apiUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 600);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    $result = json_decode($response, true);

    if ($httpCode == 200 && $result && $result['code'] === 0) {
        echo "<script>alert('任务处理完成！'); window.location.href='task_status.php?task_id=$taskId';</script>";
    } else {
        $errorMsg = $result['msg'] ?? '任务处理失败';
        echo "<script>alert('任务处理失败: $errorMsg'); window.location.href='task_status.php?task_id=$taskId';</script>";
    }
    exit;
}

// 获取任务状态
$taskStatus = $taskQueue->getTaskStatus($taskId);
$taskDetails = $taskQueue->getTaskDetails($taskId);

if (!$taskStatus) {
    die("错误：找不到指定的任务ID");
}

// 计算任务进度
$totalCount = $taskStatus['total_count'];
$doneCount = $taskStatus['done_count'];
$failedCount = $taskStatus['failed_count'];
$pendingCount = $totalCount - $doneCount - $failedCount;

$progress = $totalCount > 0 ? round(($doneCount + $failedCount) / $totalCount * 100, 1) : 0;

// 获取任务结果
$results = $taskQueue->getTaskResults($taskId);

$conn->close();
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>任务状态查询 - <?php echo htmlspecialchars($taskId); ?></title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 1200px; margin: 0 auto; }
        .status-card { background: #f5f5f5; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .progress-bar { width: 100%; height: 20px; background: #ddd; border-radius: 10px; overflow: hidden; }
        .progress-fill { height: 100%; background: #4CAF50; transition: width 0.3s ease; }
        .status-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }
        .status-item { background: white; padding: 15px; border-radius: 5px; text-align: center; border: 1px solid #ddd; }
        .status-item h3 { margin: 0 0 10px 0; color: #333; }
        .status-item .number { font-size: 24px; font-weight: bold; color: #4CAF50; }
        .button { padding: 10px 20px; background: #4CAF50; color: white; border: none; border-radius: 5px; cursor: pointer; text-decoration: none; display: inline-block; margin: 5px; }
        .button:hover { background: #45a049; }
        .button.secondary { background: #2196F3; }
        .button.secondary:hover { background: #1976D2; }
        .button[style*="background: #FF9800"]:hover { background: #F57C00 !important; }
        .results-table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        .results-table th, .results-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        .results-table th { background: #f2f2f2; }
        .status-pending { color: #FF9800; }
        .status-running { color: #2196F3; }
        .status-done { color: #4CAF50; }
        .status-failed { color: #F44336; }
    </style>
</head>
<body>
    <div class="container">
        <h1>任务状态查询</h1>

        <div class="status-card">
            <h2>任务ID: <?php echo htmlspecialchars($taskId); ?></h2>
            <p><strong>创建时间:</strong> <?php echo $taskStatus['created_at']; ?></p>
            <p><strong>最后更新:</strong> <?php echo $taskStatus['updated_at']; ?></p>

            <div class="progress-bar">
                <div class="progress-fill" style="width: <?php echo $progress; ?>%"></div>
            </div>
            <p>进度: <?php echo $progress; ?>% (<?php echo $doneCount + $failedCount; ?>/<?php echo $totalCount; ?>)</p>
        </div>

        <div class="status-grid">
            <div class="status-item">
                <h3>总数</h3>
                <div class="number"><?php echo $totalCount; ?></div>
            </div>
            <div class="status-item">
                <h3>等待中</h3>
                <div class="number status-pending"><?php echo $pendingCount; ?></div>
            </div>
            <div class="status-item">
                <h3>已完成</h3>
                <div class="number status-done"><?php echo $doneCount; ?></div>
            </div>
            <div class="status-item">
                <h3>失败</h3>
                <div class="number status-failed"><?php echo $failedCount; ?></div>
            </div>
        </div>

        <div style="text-align: center; margin: 20px 0;">
            <?php if ($pendingCount > 0): ?>
                <a href="?task_id=<?php echo urlencode($taskId); ?>&action=process" class="button">开始处理任务</a>
            <?php endif; ?>

            <?php if ($failedCount > 0 && $pendingCount == 0): ?>
                <a href="?task_id=<?php echo urlencode($taskId); ?>&action=retry" class="button" style="background: #FF9800;" onclick="return confirm('确定要重新处理失败的任务吗？')">重新处理失败任务</a>
            <?php endif; ?>

            <?php if ($doneCount > 0): ?>
                <a href="?task_id=<?php echo urlencode($taskId); ?>&action=download" class="button secondary">下载Excel结果</a>
            <?php endif; ?>

            <a href="javascript:location.reload()" class="button secondary">刷新状态</a>
            <a href="index.php" class="button secondary">返回首页</a>
        </div>

        <?php if (!empty($results)): ?>
        <h3>任务结果 (<?php echo count($results); ?> 条)</h3>
        <table class="results-table">
            <thead>
                <tr>
                    <th>链接名称</th>
                    <th>广告主账户ID</th>
                    <th>应用名称</th>
                    <th>推广链接</th>
                    <th>创建时间</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($results as $result): ?>
                <tr>
                    <td><?php echo htmlspecialchars($result['link_name']); ?></td>
                    <td><?php echo htmlspecialchars($result['advertiser_account_id']); ?></td>
                    <td><?php echo htmlspecialchars($result['app_name']); ?></td>
                    <td style="word-break: break-all;"><?php echo htmlspecialchars($result['link']); ?></td>
                    <td><?php echo $result['created_at']; ?></td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        <?php endif; ?>

        <h3>任务详情</h3>
        <table class="results-table">
            <thead>
                <tr>
                    <th>序号</th>
                    <th>链接名称</th>
                    <th>状态</th>
                    <th>错误信息</th>
                    <th>更新时间</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($taskDetails as $index => $detail): ?>
                <tr>
                    <td><?php echo $index + 1; ?></td>
                    <td><?php echo htmlspecialchars($detail['name']); ?></td>
                    <td class="status-<?php echo $detail['status']; ?>">
                        <?php
                        $statusMap = [
                            'pending' => '等待中',
                            'running' => '处理中',
                            'done' => '已完成',
                            'failed' => '失败'
                        ];
                        echo $statusMap[$detail['status']] ?? $detail['status'];
                        ?>
                    </td>
                    <td><?php echo htmlspecialchars($detail['error_message'] ?? ''); ?></td>
                    <td><?php echo $detail['updated_at']; ?></td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>

    <script>
        // 如果有待处理的任务，每30秒自动刷新页面
        <?php if ($pendingCount > 0): ?>
        setTimeout(function() {
            location.reload();
        }, 30000);
        <?php endif; ?>
    </script>
</body>
</html>
