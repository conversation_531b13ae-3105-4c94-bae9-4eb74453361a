<?php
require_once 'config.php';

echo "<!DOCTYPE html>";
echo "<html><head><meta charset='UTF-8'><title>检查失败任务</title>";
echo "<style>body{font-family:Arial,sans-serif;margin:20px;} .error{color:red;} .info{color:blue;} table{border-collapse:collapse;width:100%;} th,td{border:1px solid #ddd;padding:8px;text-align:left;}</style>";
echo "</head><body>";
echo "<h1>检查失败任务详情</h1>";

try {
    // 连接数据库
    $conn = new mysqli(DB_HOST, DB_USER, DB_PASSWORD, DB_NAME);
    if ($conn->connect_error) {
        throw new Exception("连接失败: " . $conn->connect_error);
    }
    $conn->set_charset("utf8mb4");

    // 查询失败的任务ID
    $failedTaskId = 'task_683fc5c9b780a0.29361927_1749009865';
    
    echo "<h2>任务ID: $failedTaskId</h2>";

    // 查询任务详情
    $stmt = $conn->prepare("SELECT id, name, status, error_message, created_at, updated_at FROM create_promotion_link WHERE task_id = ? ORDER BY id");
    $stmt->bind_param("s", $failedTaskId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        echo "<h3>任务记录详情:</h3>";
        echo "<table>";
        echo "<tr><th>ID</th><th>名称</th><th>状态</th><th>错误信息</th><th>创建时间</th><th>更新时间</th></tr>";
        
        while ($row = $result->fetch_assoc()) {
            $statusColor = ($row['status'] === 'failed') ? 'color:red;' : '';
            echo "<tr>";
            echo "<td>" . $row['id'] . "</td>";
            echo "<td>" . htmlspecialchars($row['name']) . "</td>";
            echo "<td style='$statusColor'>" . $row['status'] . "</td>";
            echo "<td class='error'>" . htmlspecialchars($row['error_message'] ?? '无错误信息') . "</td>";
            echo "<td>" . $row['created_at'] . "</td>";
            echo "<td>" . $row['updated_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p class='info'>没有找到该任务的记录</p>";
    }

    // 查询所有失败的任务
    echo "<h2>所有失败的任务</h2>";
    $stmt = $conn->prepare("SELECT task_id, COUNT(*) as count, GROUP_CONCAT(DISTINCT error_message SEPARATOR '; ') as errors, MIN(created_at) as created_at FROM create_promotion_link WHERE status = 'failed' GROUP BY task_id ORDER BY created_at DESC LIMIT 10");
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        echo "<table>";
        echo "<tr><th>任务ID</th><th>失败记录数</th><th>错误信息</th><th>创建时间</th></tr>";
        
        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($row['task_id']) . "</td>";
            echo "<td>" . $row['count'] . "</td>";
            echo "<td class='error'>" . htmlspecialchars($row['errors']) . "</td>";
            echo "<td>" . $row['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p class='info'>没有找到失败的任务</p>";
    }

    // 查询最近的任务状态统计
    echo "<h2>最近任务状态统计</h2>";
    $stmt = $conn->prepare("
        SELECT 
            task_id, 
            status,
            COUNT(*) as count,
            MIN(created_at) as created_at,
            MAX(updated_at) as updated_at
        FROM create_promotion_link 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
        GROUP BY task_id, status 
        ORDER BY created_at DESC
    ");
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        echo "<table>";
        echo "<tr><th>任务ID</th><th>状态</th><th>记录数</th><th>创建时间</th><th>更新时间</th></tr>";
        
        while ($row = $result->fetch_assoc()) {
            $statusColor = '';
            switch($row['status']) {
                case 'done': $statusColor = 'color:green;'; break;
                case 'failed': $statusColor = 'color:red;'; break;
                case 'running': $statusColor = 'color:blue;'; break;
                case 'pending': $statusColor = 'color:orange;'; break;
            }
            
            echo "<tr>";
            echo "<td>" . htmlspecialchars($row['task_id']) . "</td>";
            echo "<td style='$statusColor'>" . $row['status'] . "</td>";
            echo "<td>" . $row['count'] . "</td>";
            echo "<td>" . $row['created_at'] . "</td>";
            echo "<td>" . $row['updated_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p class='info'>最近1小时内没有任务</p>";
    }

    // 检查是否有正在运行的任务
    echo "<h2>当前运行状态</h2>";
    $stmt = $conn->prepare("SELECT COUNT(*) as running_count FROM create_promotion_link WHERE status = 'running'");
    $stmt->execute();
    $result = $stmt->get_result();
    $runningCount = $result->fetch_assoc()['running_count'];
    
    if ($runningCount > 0) {
        echo "<p class='error'>⚠️ 当前有 $runningCount 个任务正在运行中</p>";
    } else {
        echo "<p class='info'>✅ 当前没有正在运行的任务</p>";
    }

} catch (Exception $e) {
    echo "<p class='error'>❌ 错误: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "</body></html>";
?>
