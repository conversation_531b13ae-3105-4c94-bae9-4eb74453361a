<?php
// test_retry_failed_task.php - 测试重新处理失败任务功能
require_once 'config.php';

// 设置内部编码和页面编码
mb_internal_encoding('UTF-8');
header('Content-Type: text/html; charset=UTF-8');

// 连接数据库
$conn = new mysqli(DB_HOST, DB_USER, DB_PASSWORD, DB_NAME);
if ($conn->connect_error) {
    die("连接失败: " . $conn->connect_error);
}
$conn->set_charset("utf8mb4");

echo "<!DOCTYPE html>
<html>
<head>
    <meta charset='UTF-8'>
    <title>测试重新处理失败任务</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .info { background: #e7f3ff; padding: 10px; border-left: 4px solid #2196F3; margin: 10px 0; }
        .success { background: #e8f5e8; padding: 10px; border-left: 4px solid #4CAF50; margin: 10px 0; }
        .error { background: #ffebee; padding: 10px; border-left: 4px solid #f44336; margin: 10px 0; }
        .warning { background: #fff3e0; padding: 10px; border-left: 4px solid #ff9800; margin: 10px 0; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background: #f2f2f2; }
        .button { padding: 8px 16px; background: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; display: inline-block; margin: 5px; }
        .button:hover { background: #45a049; }
        .button.retry { background: #FF9800; }
        .button.retry:hover { background: #F57C00; }
    </style>
</head>
<body>";

echo "<h1>测试重新处理失败任务功能</h1>";

// 查找失败的任务
echo "<h2>1. 查找失败的任务</h2>";
$result = $conn->query("
    SELECT task_id, COUNT(*) as failed_count, 
           MIN(created_at) as created_at, MAX(updated_at) as updated_at,
           GROUP_CONCAT(error_message SEPARATOR '; ') as error_messages
    FROM create_promotion_link 
    WHERE status = 'failed' 
      AND created_at >= DATE_SUB(NOW(), INTERVAL 2 HOUR)
    GROUP BY task_id 
    ORDER BY created_at DESC
    LIMIT 5
");

if ($result->num_rows > 0) {
    echo "<table>";
    echo "<tr><th>任务ID</th><th>失败记录数</th><th>创建时间</th><th>更新时间</th><th>错误信息</th><th>操作</th></tr>";
    
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($row['task_id']) . "</td>";
        echo "<td>" . $row['failed_count'] . "</td>";
        echo "<td>" . $row['created_at'] . "</td>";
        echo "<td>" . $row['updated_at'] . "</td>";
        echo "<td>" . htmlspecialchars($row['error_messages']) . "</td>";
        echo "<td>";
        echo "<a href='task_status.php?task_id=" . urlencode($row['task_id']) . "' class='button' target='_blank'>查看状态</a>";
        echo "<a href='?action=test_retry&task_id=" . urlencode($row['task_id']) . "' class='button retry' onclick='return confirm(\"确定要测试重新处理这个失败任务吗？\")'>测试重新处理</a>";
        echo "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p class='info'>没有找到最近2小时内的失败任务</p>";
}

// 处理测试重新处理请求
if (isset($_GET['action']) && $_GET['action'] === 'test_retry' && isset($_GET['task_id'])) {
    $taskId = $_GET['task_id'];
    
    echo "<h2>2. 测试重新处理任务: " . htmlspecialchars($taskId) . "</h2>";
    
    // 检查任务是否存在且为失败状态
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM create_promotion_link WHERE task_id = ? AND status = 'failed'");
    $stmt->bind_param("s", $taskId);
    $stmt->execute();
    $result = $stmt->get_result();
    $row = $result->fetch_assoc();
    
    if ($row['count'] == 0) {
        echo "<p class='error'>❌ 没有找到失败状态的任务记录</p>";
    } else {
        echo "<p class='info'>📋 找到 {$row['count']} 条失败记录</p>";
        
        // 步骤1: 重置任务状态为pending
        echo "<h3>步骤1: 重置任务状态</h3>";
        $stmt = $conn->prepare("UPDATE create_promotion_link SET status = 'pending', error_message = NULL, updated_at = NOW() WHERE task_id = ? AND status = 'failed'");
        $stmt->bind_param("s", $taskId);
        
        if ($stmt->execute() && $stmt->affected_rows > 0) {
            echo "<p class='success'>✅ 已重置 {$stmt->affected_rows} 条记录状态为pending</p>";
            
            // 步骤2: 调用任务处理器
            echo "<h3>步骤2: 调用任务处理器</h3>";
            
            $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
            $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
            $baseUrl = $protocol . '://' . $host . dirname($_SERVER['REQUEST_URI']);
            $apiUrl = $baseUrl . '/modules/task_queue_processor.php';
            
            echo "<p class='info'>🔗 API URL: $apiUrl</p>";
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $apiUrl);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 600);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            
            $startTime = microtime(true);
            $response = curl_exec($ch);
            $endTime = microtime(true);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            curl_close($ch);
            
            $executionTime = round($endTime - $startTime, 2);
            
            echo "<p class='info'>⏱️ 执行时间: {$executionTime} 秒</p>";
            echo "<p class='info'>📊 HTTP状态码: $httpCode</p>";
            
            if ($httpCode == 200) {
                $result = json_decode($response, true);
                
                if ($result && $result['code'] === 0) {
                    echo "<p class='success'>✅ 任务处理成功！</p>";
                    echo "<div class='success'>";
                    echo "<h4>处理结果:</h4>";
                    echo "<ul>";
                    echo "<li>任务ID: " . htmlspecialchars($result['data']['task_id']) . "</li>";
                    echo "<li>总记录数: " . $result['data']['total_records'] . "</li>";
                    echo "<li>已处理: " . $result['data']['processed'] . "</li>";
                    echo "<li>成功: " . $result['data']['success'] . "</li>";
                    echo "<li>失败: " . $result['data']['failed'] . "</li>";
                    echo "<li>执行时间: " . $result['data']['execution_time'] . " 秒</li>";
                    echo "</ul>";
                    echo "</div>";
                    
                    if (!empty($result['data']['errors'])) {
                        echo "<div class='warning'>";
                        echo "<h4>错误详情:</h4>";
                        echo "<ul>";
                        foreach ($result['data']['errors'] as $error) {
                            echo "<li>" . htmlspecialchars($error['error']) . "</li>";
                        }
                        echo "</ul>";
                        echo "</div>";
                    }
                } else {
                    $errorMsg = $result['msg'] ?? '未知错误';
                    echo "<p class='error'>❌ 任务处理失败: " . htmlspecialchars($errorMsg) . "</p>";
                    echo "<p class='info'>📄 原始响应: " . htmlspecialchars($response) . "</p>";
                }
            } else {
                echo "<p class='error'>❌ HTTP请求失败，状态码: $httpCode</p>";
                echo "<p class='info'>📄 响应内容: " . htmlspecialchars($response) . "</p>";
            }
            
            // 步骤3: 查看最终状态
            echo "<h3>步骤3: 查看最终状态</h3>";
            $stmt = $conn->prepare("
                SELECT status, COUNT(*) as count, 
                       GROUP_CONCAT(error_message SEPARATOR '; ') as error_messages
                FROM create_promotion_link 
                WHERE task_id = ? 
                GROUP BY status
            ");
            $stmt->bind_param("s", $taskId);
            $stmt->execute();
            $result = $stmt->get_result();
            
            echo "<table>";
            echo "<tr><th>状态</th><th>记录数</th><th>错误信息</th></tr>";
            while ($row = $result->fetch_assoc()) {
                $statusColor = '';
                switch($row['status']) {
                    case 'done': $statusColor = 'color:green;'; break;
                    case 'failed': $statusColor = 'color:red;'; break;
                    case 'running': $statusColor = 'color:blue;'; break;
                    case 'pending': $statusColor = 'color:orange;'; break;
                }
                
                echo "<tr>";
                echo "<td style='$statusColor'>" . $row['status'] . "</td>";
                echo "<td>" . $row['count'] . "</td>";
                echo "<td>" . htmlspecialchars($row['error_messages'] ?? '') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            echo "<p class='success'>🎉 测试完成！<a href='task_status.php?task_id=" . urlencode($taskId) . "' class='button' target='_blank'>查看任务状态页面</a></p>";
            
        } else {
            echo "<p class='error'>❌ 重置任务状态失败</p>";
        }
    }
}

echo "<p><a href='index.php' class='button'>返回首页</a></p>";

echo "</body></html>";

$conn->close();
?>
