# 项目文件整理报告

## 整理时间
**执行时间**: 2025-06-04

## 整理目标
将项目中的临时测试文件和不影响核心功能的文件移动到 `bak` 文件夹中，保持项目目录的整洁。

## 核心功能文件（保留）

### 主要功能文件
- `index.php` - 系统主页面，创建推广链接的核心界面
- `config.php` - 系统配置文件，包含数据库和API配置
- `task_status.php` - 任务状态查询页面，支持重新处理失败任务

### 核心类文件
- `classes/TaskQueue.php` - 任务队列管理类

### API模块文件
- `modules/create_promotion_link.php` - 创建推广链接API
- `modules/get_promotion_links.php` - 获取推广链接API
- `modules/get_recharge_templates.php` - 获取充值模板API
- `modules/get_return_rules.php` - 获取回传规则API
- `modules/batch_create_promotion_links.php` - 批量创建推广链接API
- `modules/task_queue_processor.php` - 任务队列处理器

### 数据库文件
- `database/` 目录下的所有SQL文件（数据库结构和索引）
- `sql/add_task_queue_columns.sql` - 任务队列相关表结构

## 已移动到bak目录的文件（共42个）

### 测试文件（test_开头，共20个）
- `test_api_timestamp.php` - API时间戳测试
- `test_auto_process.php` - 自动处理测试
- `test_batch_create_links.php` - 批量创建链接测试
- `test_batch_promotion.php` - 批量推广测试
- `test_chapter_default.php` - 章节默认值测试
- `test_complete_fix.php` - 完整修复测试
- `test_complete_flow.php` - 完整流程测试
- `test_complete_retry_flow.php` - 完整重试流程测试
- `test_concurrent_tasks.php` - 并发任务测试
- `test_create_api.php` - 创建API测试
- `test_data_storage.php` - 数据存储测试
- `test_direct_submit.php` - 直接提交测试
- `test_excel_download.php` - Excel下载测试
- `test_final_flow.php` - 最终流程测试
- `test_fixed_api.php` - 修复后API测试
- `test_fixes.php` - 修复测试
- `test_form_submit.php` - 表单提交测试
- `test_interval_timing.php` - 间隔时间测试
- `test_manual_process.php` - 手动处理测试
- `test_name_fix.php` - 名称修复测试
- `test_name_insert.php` - 名称插入测试
- `test_retry_failed_task.php` - 重试失败任务测试
- `test_single_create.php` - 单个创建测试
- `test_submit_flow.php` - 提交流程测试
- `test_task_processor.php` - 任务处理器测试
- `test_timing_simple.php` - 简单时间测试
- `test_insert.php` - 插入测试（来自modules目录）

### 验证文件（verify_开头，共2个）
- `verify_chapter_fix.php` - 章节修复验证
- `verify_retry_fix.php` - 重试修复验证

### 检查文件（check_开头，共2个）
- `check_failed_task.php` - 检查失败任务
- `check_status.php` - 检查状态

### 清理文件（clean_开头，共1个）
- `clean_test_data.php` - 清理测试数据

### 调试文件（debug_开头，共1个）
- `debug_encoding.php` - 编码调试

### 备份文件（副本，共2个）
- `get_recharge_templates-副本appid带.php` - 充值模板API备份
- `get_return_rules-副本.php` - 回传规则API备份

### 其他临时文件（共14个）
- `add_test_data.php` - 添加测试数据
- `batch_create_links.php` - 批量创建链接（临时）
- `db_test.php` - 数据库测试
- `download_excel.php` - Excel下载（临时）
- `functions.php` - 通用函数（未使用）
- `index.html` - 静态HTML页面
- `layout_test.html` - 布局测试页面

## 整理效果

### 整理前
- 项目根目录包含大量测试和临时文件
- 文件混杂，难以区分核心功能和测试代码
- 总计约60+个文件

### 整理后
- **核心功能文件**: 19个（保留在主目录）
- **测试/临时文件**: 42个（移动到bak目录）
- 项目结构清晰，便于维护和部署

## 目录结构（整理后）

```
qmxs/
├── bak/                          # 备份目录（测试和临时文件）
│   ├── test_*.php               # 所有测试文件
│   ├── verify_*.php             # 验证文件
│   ├── check_*.php              # 检查文件
│   ├── clean_*.php              # 清理文件
│   ├── debug_*.php              # 调试文件
│   └── 其他临时文件...
├── classes/
│   └── TaskQueue.php            # 任务队列类
├── database/
│   └── *.sql                    # 数据库文件
├── modules/
│   ├── create_promotion_link.php
│   ├── get_promotion_links.php
│   ├── get_recharge_templates.php
│   ├── get_return_rules.php
│   ├── batch_create_promotion_links.php
│   └── task_queue_processor.php
├── sql/
│   └── add_task_queue_columns.sql
├── config.php                   # 配置文件
├── index.php                    # 主页面
└── task_status.php              # 任务状态页面
```

## 注意事项

1. **bak目录中的文件**: 这些文件是开发和测试过程中创建的，包含了大量的测试逻辑和验证代码，对于理解系统开发过程很有价值，但不影响生产环境运行。

2. **核心功能完整性**: 所有核心功能文件都已保留，系统的主要功能不受影响：
   - 推广链接创建
   - 任务队列处理
   - 失败任务重试
   - Excel结果下载
   - API接口调用

3. **恢复方法**: 如果需要恢复某个测试文件，可以从bak目录中复制回主目录。

4. **部署建议**: 在生产环境部署时，可以不包含bak目录，进一步减少文件数量。

## 总结

通过本次整理，项目目录结构更加清晰，核心功能文件和测试文件分离，便于：
- 代码维护和更新
- 生产环境部署
- 新开发人员理解项目结构
- 版本控制管理

所有核心功能保持完整，系统可以正常运行。
