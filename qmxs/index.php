<?php
// index.php - 创建推广链接系统主页面

// 设置内部编码
mb_internal_encoding('UTF-8');

// 引入配置文件
require_once 'config.php';

// Excel生成和下载函数
function generateAndDownloadExcel($batchData) {
    // 设置Excel文件头 - 使用.xls格式以确保兼容性
    $filename = 'promotion_links_' . date('Y-m-d_H-i-s') . '.xls';

    // 连接数据库获取详细信息
    $conn = new mysqli(DB_HOST, DB_USER, DB_PASSWORD, DB_NAME);
    if ($conn->connect_error) {
        error_log("Excel生成时数据库连接失败: " . $conn->connect_error);
    }
    $conn->set_charset("utf8mb4");

    // 开始输出Excel内容
    header('Content-Type: application/vnd.ms-excel');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: max-age=0');
    header('Pragma: public');

    // 输出Excel XML格式
    echo '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
    echo '<?mso-application progid="Excel.Sheet"?>' . "\n";
    echo '<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet"' . "\n";
    echo ' xmlns:o="urn:schemas-microsoft-com:office:office"' . "\n";
    echo ' xmlns:x="urn:schemas-microsoft-com:office:excel"' . "\n";
    echo ' xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet"' . "\n";
    echo ' xmlns:html="http://www.w3.org/TR/REC-html40">' . "\n";

    // 定义样式
    echo '<Styles>' . "\n";
    echo '<Style ss:ID="Header">' . "\n";
    echo '<Font ss:Bold="1"/>' . "\n";
    echo '<Interior ss:Color="#CCE5FF" ss:Pattern="Solid"/>' . "\n";
    echo '<Borders>' . "\n";
    echo '<Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>' . "\n";
    echo '<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>' . "\n";
    echo '<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>' . "\n";
    echo '<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>' . "\n";
    echo '</Borders>' . "\n";
    echo '</Style>' . "\n";
    echo '<Style ss:ID="Data">' . "\n";
    echo '<Borders>' . "\n";
    echo '<Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>' . "\n";
    echo '<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>' . "\n";
    echo '<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>' . "\n";
    echo '<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>' . "\n";
    echo '</Borders>' . "\n";
    echo '</Style>' . "\n";
    echo '</Styles>' . "\n";

    echo '<Worksheet ss:Name="推广链接">' . "\n";
    echo '<Table>' . "\n";

    // 设置列宽
    echo '<Column ss:Width="120"/>' . "\n"; // 广告主账户ID
    echo '<Column ss:Width="100"/>' . "\n"; // 小程序名称
    echo '<Column ss:Width="180"/>' . "\n"; // 小程序原始ID
    echo '<Column ss:Width="300"/>' . "\n"; // 小程序链接
    echo '<Column ss:Width="150"/>' . "\n"; // 链接名称

    // 表头
    echo '<Row>' . "\n";
    echo '<Cell ss:StyleID="Header"><Data ss:Type="String">广告主账户ID</Data></Cell>' . "\n";
    echo '<Cell ss:StyleID="Header"><Data ss:Type="String">小程序名称(11位)</Data></Cell>' . "\n";
    echo '<Cell ss:StyleID="Header"><Data ss:Type="String">小程序原始ID</Data></Cell>' . "\n";
    echo '<Cell ss:StyleID="Header"><Data ss:Type="String">小程序链接(32位)</Data></Cell>' . "\n";
    echo '<Cell ss:StyleID="Header"><Data ss:Type="String">链接名称</Data></Cell>' . "\n";
    echo '</Row>' . "\n";

    // 数据行 - 只添加成功的记录
    if (!empty($batchData['results'])) {
        foreach ($batchData['results'] as $result) {
            $linkData = $result['link_data'];

            // 从link表查询详细信息
            $linkId = $linkData['id'] ?? '';
            $appName = '';
            $appId = '';
            $linkUrl = $linkData['link'] ?? '';
            $linkName = $result['name'] ?? '';
            $advertiserId = '';

            if ($conn && $linkUrl) {
                $stmt = $conn->prepare("SELECT app_name, appid, advertiser_account_id FROM link WHERE link = ? LIMIT 1");
                $stmt->bind_param("s", $linkUrl);
                $stmt->execute();
                $linkResult = $stmt->get_result();
                if ($linkRow = $linkResult->fetch_assoc()) {
                    $appName = $linkRow['app_name'] ?? '';
                    $appId = $linkRow['appid'] ?? '';
                    $advertiserId = $linkRow['advertiser_account_id'] ?? '';
                }
            }

            // 如果没有查到数据，使用默认值
            if (empty($appName)) {
                $appName = '七猫小说';
            }
            if (empty($appId)) {
                $appId = 'wxe3a874175a6e6ed3';
            }

            echo '<Row>' . "\n";
            echo '<Cell ss:StyleID="Data"><Data ss:Type="String">' . htmlspecialchars($advertiserId) . '</Data></Cell>' . "\n";
            echo '<Cell ss:StyleID="Data"><Data ss:Type="String">' . htmlspecialchars($appName) . '</Data></Cell>' . "\n";
            echo '<Cell ss:StyleID="Data"><Data ss:Type="String">' . htmlspecialchars($appId) . '</Data></Cell>' . "\n";
            echo '<Cell ss:StyleID="Data"><Data ss:Type="String">' . htmlspecialchars($linkUrl) . '</Data></Cell>' . "\n";
            echo '<Cell ss:StyleID="Data"><Data ss:Type="String">' . htmlspecialchars($linkName) . '</Data></Cell>' . "\n";
            echo '</Row>' . "\n";
        }
    }

    echo '</Table>' . "\n";
    echo '</Worksheet>' . "\n";
    echo '</Workbook>' . "\n";

    if ($conn) {
        $conn->close();
    }
}

// 处理AJAX请求：根据选择的用户获取对应的模板和规则名称
if (isset($_GET['action']) && $_GET['action'] === 'get_options') {
    $accountName = $_GET['account_name'] ?? '';
    $conn = new mysqli(DB_HOST, DB_USER, DB_PASSWORD, DB_NAME);
    if ($conn->connect_error) {
        die("连接失败: " . $conn->connect_error);
    }

    // 查询关联的充值模板名称
    $sqlTemplate = "SELECT DISTINCT template_name FROM recharge_templates WHERE account_name = ?";
    $stmt = $conn->prepare($sqlTemplate);
    $stmt->bind_param("s", $accountName);
    $stmt->execute();
    $resultTemplate = $stmt->get_result();
    $templates = [];
    while ($row = $resultTemplate->fetch_assoc()) {
        $templates[] = $row['template_name'];
    }

    // 查询关联的回传规则名称
    $sqlRule = "SELECT DISTINCT rule_name FROM postback_rules WHERE account_name = ?";
    $stmt = $conn->prepare($sqlRule);
    $stmt->bind_param("s", $accountName);
    $stmt->execute();
    $resultRule = $stmt->get_result();
    $rules = [];
    while ($row = $resultRule->fetch_assoc()) {
        $rules[] = $row['rule_name'];
    }

    $conn->close();
    echo json_encode(['templates' => $templates, 'rules' => $rules]);
    exit;
}

// 设置内部编码和页面编码
mb_internal_encoding('UTF-8');
header('Content-Type: text/html; charset=UTF-8');

// 处理表单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // 强制转换输入编码，使用更可靠的编码检测
    $accountName = mb_convert_encoding($_POST['recharge_account_name'] ?? '', 'UTF-8', mb_detect_encoding($_POST['recharge_account_name'] ?? '', 'UTF-8, GBK, GB2312, BIG5', true));
    $templateName = mb_convert_encoding($_POST['recharge_panel_name'] ?? '', 'UTF-8', mb_detect_encoding($_POST['recharge_panel_name'] ?? '', 'UTF-8, GBK, GB2312, BIG5', true));
    $ruleName = mb_convert_encoding($_POST['postback_rule_name'] ?? '', 'UTF-8', mb_detect_encoding($_POST['postback_rule_name'] ?? '', 'UTF-8, GBK, GB2312, BIG5', true));
    $project = $_POST['project'] ?? 0;
    $appName = $_POST['app_name'] ?? '';
    $mediaId = $_POST['media_id'] ?? 0;
    $bookNames = $_POST['book_names'] ?? '';
    $advertiserAccountIds = $_POST['advertiser_account_ids'] ?? '';
    $bookIds = $_POST['book_ids'] ?? '';
    $chapterNumbers = $_POST['chapter_numbers'] ?? '';

    // 对 book_names 做更严格的编码转换和校验
    $bookNames = mb_convert_encoding($bookNames, 'UTF-8', mb_detect_encoding($bookNames, 'UTF-8, GBK, GB2312, BIG5', true));

    // 先处理 book_names 以确定行数
    $names = array_filter(array_map(function($item) {
        $trimmed = trim($item);
        return mb_convert_encoding($trimmed, 'UTF-8', mb_detect_encoding($trimmed, 'UTF-8, GBK, GB2312, BIG5', true));
    }, explode("\n", $bookNames)));

    $validNames = array_filter($names, function($name) {
        return !empty($name) && mb_check_encoding($name, 'UTF-8');
    });

    if (empty($validNames)) {
        throw new Exception("链接名称不能为空或包含无效字符，请检查输入！");
    }

    $nameCount = count($validNames);

    // 修复：根据链接名称的行数来设置默认值
    if (empty($bookIds)) {
        $bookIds = str_repeat("1\n", $nameCount - 1) . "1"; // 生成对应行数的 "1"
        error_log("自动生成 book_ids: " . $bookIds . " (共 $nameCount 行)");
    } else {
        // 检查用户输入的行数是否足够，不够则补充默认值
        $userBookIds = array_map('trim', explode("\n", $bookIds));
        $userBookIdsCount = count(array_filter($userBookIds, function($id) { return !empty($id); }));
        if ($userBookIdsCount < $nameCount) {
            $needMore = $nameCount - $userBookIdsCount;
            $bookIds .= "\n" . str_repeat("1\n", $needMore - 1) . "1";
            error_log("补充 book_ids: 用户输入 $userBookIdsCount 行，需要 $nameCount 行，补充了 $needMore 行");
        }
    }

    if (empty($chapterNumbers)) {
        $chapterNumbers = str_repeat("1\n", $nameCount - 1) . "1"; // 生成对应行数的 "1"
        error_log("自动生成 chapter_numbers: " . $chapterNumbers . " (共 $nameCount 行)");
    } else {
        // 检查用户输入的行数是否足够，不够则补充默认值
        $userChapterNumbers = array_map('trim', explode("\n", $chapterNumbers));
        $userChapterNumbersCount = count(array_filter($userChapterNumbers, function($ch) { return !empty($ch); }));
        if ($userChapterNumbersCount < $nameCount) {
            $needMore = $nameCount - $userChapterNumbersCount;
            $chapterNumbers .= "\n" . str_repeat("1\n", $needMore - 1) . "1";
            error_log("补充 chapter_numbers: 用户输入 $userChapterNumbersCount 行，需要 $nameCount 行，补充了 $needMore 行");
        }
    }

    // 连接数据库
    $conn = new mysqli(DB_HOST, DB_USER, DB_PASSWORD, DB_NAME);
    if ($conn->connect_error) {
        die("连接失败: " . $conn->connect_error);
    }

    // 设置字符集（确保数据库连接使用 utf8mb4）
    $conn->set_charset("utf8mb4");
    $conn->query("SET NAMES 'utf8mb4'");

    try {
        // 开启事务
        $conn->begin_transaction();

        // 获取子账号ID
        $stmt = $conn->prepare("SELECT account_id FROM recharge_templates WHERE account_name = ?");
        $stmt->bind_param("s", $accountName);
        $stmt->execute();
        $result = $stmt->get_result();
        $accountId = $result->fetch_assoc()['account_id'] ?? 0;

        // 获取充值模板ID
        $stmt = $conn->prepare("SELECT template_id FROM recharge_templates WHERE template_name = ?");
        $stmt->bind_param("s", $templateName);
        $stmt->execute();
        $result = $stmt->get_result();
        $rechargePanelId = $result->fetch_assoc()['template_id'] ?? 0;

        // 获取回传规则ID
        $stmt = $conn->prepare("SELECT rule_id FROM postback_rules WHERE rule_name = ?");
        $stmt->bind_param("s", $ruleName);
        $stmt->execute();
        $result = $stmt->get_result();
        $postbackRuleId = $result->fetch_assoc()['rule_id'] ?? 0;

        // 获取小程序app_id
        $stmt = $conn->prepare("SELECT appid FROM recharge_templates WHERE app_name = ?");
        $stmt->bind_param("s", $appName);
        $stmt->execute();
        $result = $stmt->get_result();
        $appId = $result->fetch_assoc()['appid'] ?? '';

        // 校验 app_id 是否为空
        if (empty($appId)) {
            throw new Exception("应用名称未关联有效AppID，请选择正确的应用名称");
        }

        // 引入任务队列类
        require_once 'classes/TaskQueue.php';
        $taskQueue = new TaskQueue($conn);

        // 准备任务数据
        $taskData = [];

        // validNames 已在前面处理过，这里直接使用

        // 将 book_ids 和 chapter_numbers 转换为数组
        $ids = array_map('trim', explode("\n", $bookIds));
        $chapters = array_map('trim', explode("\n", $chapterNumbers));
        $advertiserIds = array_map('trim', explode("\n", $advertiserAccountIds));

        foreach ($validNames as $index => $name) {
            // 调试信息：记录每个name的详细信息
            error_log("处理name[$index]: " . json_encode([
                'original' => $name,
                'length' => mb_strlen($name),
                'bytes' => strlen($name),
                'encoding' => mb_detect_encoding($name),
                'is_utf8' => mb_check_encoding($name, 'UTF-8')
            ]));

            $bookId = $ids[$index] ?? 0;
            $chapterNum = $chapters[$index] ?? 0;
            $advertiserId = $advertiserIds[$index] ?? 0;

            // 准备任务数据
            $taskData[] = [
                'admin_account_name' => ADMIN_ACCOUNT_NAME,
                'account_id' => $accountId,
                'project' => $project,
                'appid' => $appId,
                'book_id' => $bookId,
                'chapter_num' => $chapterNum,
                'name' => $name,
                'media_id' => $mediaId,
                'postback_rule_id' => $postbackRuleId,
                'recharge_panel_id' => $rechargePanelId,
                'advertiser_account_id' => $advertiserId
            ];
        }

        // 创建任务
        $taskId = $taskQueue->createTask($taskData);
        error_log("任务创建成功，任务ID: " . $taskId);

        // 提交事务
        $conn->commit();
        $conn->close();

        // 显示任务创建成功页面，并提供任务状态查询链接
        echo "<script>
            alert('任务创建成功！\\n任务ID: $taskId\\n\\n任务已加入队列，系统将自动处理。\\n您可以通过任务ID查询处理状态和下载结果。');
            window.location.href='task_status.php?task_id=$taskId';
        </script>";
        exit;
    } catch (Exception $e) {
        // 回滚事务
        $conn->rollback();
        echo "数据保存失败: " . $e->getMessage();
    }

    $conn->close();
    exit;
}

// 检查用户请求的功能
if (isset($_GET['action'])) {
    $action = $_GET['action'];

} else {
    // 新增数据库连接和模板名称查询
    $conn = new mysqli(DB_HOST, DB_USER, DB_PASSWORD, DB_NAME);
    if ($conn->connect_error) {
        die("连接失败: " . $conn->connect_error);
    }

    // 查询去重的充值模板名称
    $sql = "SELECT DISTINCT template_name FROM recharge_templates WHERE template_name IS NOT NULL AND template_name != ''";
    $result = $conn->query($sql);
    $templateOptions = '';
    if ($result && $result->num_rows > 0) {
        while($row = $result->fetch_assoc()) {
            $templateOptions .= "<option value='{$row['template_name']}'>{$row['template_name']}</option>";
        }
    } else {
        $templateOptions = "<option value=''>暂无充值模板</option>";
    }

    // 新增：查询去重的用户账户名称
    $sql_account = "SELECT DISTINCT account_name FROM recharge_templates WHERE account_name IS NOT NULL AND account_name != ''";
    $result_account = $conn->query($sql_account);
    $accountOptions = '';
    if ($result_account && $result_account->num_rows > 0) {
        while($row = $result_account->fetch_assoc()) {
            $accountOptions .= "<option value='{$row['account_name']}'>{$row['account_name']}</option>";
        }
    } else {
        $accountOptions = "<option value=''>暂无用户</option>";
    }

    // 新增：查询去重的回传规则名称
    $sql_rule = "SELECT DISTINCT rule_name FROM postback_rules WHERE rule_name IS NOT NULL AND rule_name != ''";
    $result_rule = $conn->query($sql_rule);
    $ruleOptions = '';
    if ($result_rule && $result_rule->num_rows > 0) {
        while($row = $result_rule->fetch_assoc()) {
            $ruleOptions .= "<option value='{$row['rule_name']}'>{$row['rule_name']}</option>";
        }
    } else {
        $ruleOptions = "<option value=''>暂无回传规则</option>";
    }

    // 新增：查询去重的分销项目标识
    $sql_project = "SELECT DISTINCT project FROM recharge_templates WHERE project IS NOT NULL AND project != ''";
    $result_project = $conn->query($sql_project);
    $projectOptions = '';
    if ($result_project && $result_project->num_rows > 0) {
        while($row = $result_project->fetch_assoc()) {
            $projectOptions .= "<option value='{$row['project']}'>{$row['project']}</option>";
        }
    } else {
        $projectOptions = "<option value=''>暂无项目标识</option>";
    }

    // 新增：查询去重的应用名称
    $sql_app_name = "SELECT DISTINCT app_name FROM recharge_templates WHERE app_name IS NOT NULL AND app_name != ''";
    $result_app_name = $conn->query($sql_app_name);
    $appNameOptions = '';
    if ($result_app_name && $result_app_name->num_rows > 0) {
        while($row = $result_app_name->fetch_assoc()) {
            $appNameOptions .= "<option value='{$row['app_name']}'>{$row['app_name']}</option>";
        }
    } else {
        $appNameOptions = "<option value=''>暂无应用名称</option>";
    }

    // 新增：媒体标识下拉选项
    $mediaOptions = '';
    $mediaList = [1 => '巨量', 2 => '广点通', 3 => '百度', 4 => '微博', 5 => 'B站'];
    foreach ($mediaList as $id => $name) {
        $selected = ($id == MEDIA_ID) ? 'selected' : '';
        $mediaOptions .= "<option value='{$id}' {$selected}>{$name}</option>";
    }
    $conn->close();

    // 页面HTML部分
    echo "<!DOCTYPE html>";
    echo "<html lang='zh-CN'>";
    echo "<head>";
    echo "<meta charset='UTF-8'>";
    echo "<meta http-equiv='Content-Type' content='text/html; charset=UTF-8'>";
    echo "<title>创建推广链接</title>";
    echo "<style>";
    echo "body { font-family: Arial, sans-serif; }";
    echo ".form-group { margin-bottom: 15px; }";
    echo "label { display: block; margin-bottom: 5px; }";
    echo "input, select { width: 100%; padding: 8px; box-sizing: border-box; }";
    echo "textarea { width: 100%; padding: 8px; box-sizing: border-box; resize: vertical; }";
    echo "button { padding: 10px 15px; background-color: #4CAF50; color: white; border: none; cursor: pointer; }";
    echo "button:hover { background-color: #45a049; }";
    echo ".textarea-row { display: flex; gap: 15px; margin-bottom: 15px; }";
    echo ".textarea-col { flex: 1; }";
    echo ".textarea-col label { margin-bottom: 5px; }";
    echo ".textarea-col textarea { height: 150px; }";
    echo "@media (max-width: 768px) { .textarea-row { flex-direction: column; gap: 10px; } }";
    echo "</style>";
    echo "</head>";
    echo "<body>";
    echo "<h1>批量创建链接(七猫)</h1>";
    echo "<form action='' method='post' accept-charset='UTF-8'>";
    echo "<input type='hidden' name='charset' value='UTF-8'>";

    // 新增：用户选择下拉框
    echo "<div class='form-group'>";
    echo "<label for='recharge_account_name'>选择用户</label>";
    echo "<select id='recharge_account_name' name='recharge_account_name'>";
    echo $accountOptions;
    echo "</select>";
    echo "</div>";

    // 新增：选择充值模板下拉框
    echo "<div class='form-group'>";
    echo "<label for='recharge_panel_name'>选择充值模板</label>";
    echo "<select id='recharge_panel_name' name='recharge_panel_name'>";
    echo $templateOptions;
    echo "</select>";
    echo "</div>";

    // 新增：选择回传规则下拉框（位于用户选择下方）
    echo "<div class='form-group'>";
    echo "<label for='postback_rule_name'>选择回传规则</label>";
    echo "<select id='postback_rule_name' name='postback_rule_name'>";
    echo $ruleOptions;
    echo "</select>";
    echo "</div>";

    // 新增：分销项目标识下拉框
    echo "<div class='form-group'>";
    echo "<label for='project'>分销项目标识</label>";
    echo "<select id='project' name='project'>";
    echo $projectOptions;
    echo "</select>";
    echo "</div>";

    // 新增：应用名称下拉框
    echo "<div class='form-group'>";
    echo "<label for='app_name'>应用名称</label>";
    echo "<select id='app_name' name='app_name'>";
    echo $appNameOptions;
    echo "</select>";
    echo "</div>";

    // 新增：媒体标识下拉框
    echo "<div class='form-group'>";
    echo "<label for='media_id'>媒体标识</label>";
    echo "<select id='media_id' name='media_id'>";
    echo $mediaOptions;
    echo "</select>";
    echo "</div>";

    // 4个输入框横向排列
    echo "<div class='textarea-row'>";

    // 1. 推广链接名称
    echo "<div class='textarea-col'>";
    echo "<label for='book_names'>推广链接名称（一行一个名称）<span id='book_names_count' style='color: red;'>(已输入0个名称)</span></label>";
    echo "<textarea id='book_names' name='book_names' placeholder='请输入名称，每行一个名称，支持多行输入'></textarea>";
    echo "</div>";

    // 2. 广告主账户ID
    echo "<div class='textarea-col'>";
    echo "<label for='advertiser_account_ids'>广告主账户ID（一行一个ID）<span id='advertiser_ids_count' style='color: red;'>(已输入0个广告主账户ID)</span></label>";
    echo "<textarea id='advertiser_account_ids' name='advertiser_account_ids' placeholder='请输入广告主账户ID，每行一个广告主账户ID'></textarea>";
    echo "</div>";

    // 3. 投放书籍ID
    echo "<div class='textarea-col'>";
    echo "<label for='book_ids'>投放书籍ID（一行一个ID）<span id='book_ids_count' style='color: red;'>(已输入0个书籍ID)</span></label>";
    echo "<textarea id='book_ids' name='book_ids' placeholder='请输入投放书籍ID，每行一个书籍ID'></textarea>";
    echo "</div>";

    // 4. 投放章节序号
    echo "<div class='textarea-col'>";
    echo "<label for='chapter_numbers'>投放章节序号ID（一行一个ID）<span id='chapter_numbers_count' style='color: red;'>(已输入0个章节序号ID)</span></label>";
    echo "<textarea id='chapter_numbers' name='chapter_numbers' placeholder='默认章节序号ID为：1'></textarea>";
    echo "</div>";

    echo "</div>";

    // 新增：动态加载脚本
    echo <<<JS
<script>
// 用户选择变化时动态加载相关数据
document.getElementById('recharge_account_name').addEventListener('change', function() {
    const accountName = this.value;
    if (!accountName) return;

    fetch(`?action=get_options&account_name=\${encodeURIComponent(accountName)}`)
        .then(response => response.json())
        .then(data => {
            // 更新充值模板下拉框
            const templateSelect = document.getElementById('recharge_panel_name');
            templateSelect.innerHTML = data.templates.map(t => `<option value="\${t}">\${t}</option>`).join('');

            // 更新回传规则下拉框
            const ruleSelect = document.getElementById('postback_rule_name');
            ruleSelect.innerHTML = data.rules.map(r => `<option value="\${r}">\${r}</option>`).join('');

            // 新增：更新分销项目标识下拉框
            const projectSelect = document.getElementById('project');
            projectSelect.innerHTML = data.projects.map(p => `<option value="\${p}">\${p}</option>`).join('');

            // 新增：更新应用名称下拉框
            const appNameSelect = document.getElementById('app_name');
            appNameSelect.innerHTML = data.app_names.map(a => `<option value="\${a}">\${a}</option>`).join('');
        });
});

// 输入框计数功能
function updateCount(textareaId, countId, label) {
    const textarea = document.getElementById(textareaId);
    const countSpan = document.getElementById(countId);

    function updateCounter() {
        const lines = textarea.value.split('\\n').filter(line => line.trim() !== '');
        countSpan.textContent = `(已输入\${lines.length}个\${label})`;
    }

    textarea.addEventListener('input', updateCounter);
    textarea.addEventListener('paste', function() {
        setTimeout(updateCounter, 10);
    });
}

// 页面加载完成后初始化计数器
document.addEventListener('DOMContentLoaded', function() {
    updateCount('book_names', 'book_names_count', '名称');
    updateCount('advertiser_account_ids', 'advertiser_ids_count', '广告主账户ID');
    updateCount('book_ids', 'book_ids_count', '书籍ID');
    updateCount('chapter_numbers', 'chapter_numbers_count', '章节序号ID');
});
</script>
JS;

    // 在</form>标签前新增提交按钮
    echo "<div class='form-group'>";
    echo "<button type='submit'>提交</button>";
    echo "</div>";

    echo "</form>";

    // 添加页面加载时自动执行API调用的JavaScript
    echo <<<AUTOLOAD_JS
<script>
// 页面加载完成后自动执行API调用
document.addEventListener('DOMContentLoaded', function() {
    // 检查是否需要执行自动API调用（首次访问或刷新）
    executeAutoApiCalls();
});

function executeAutoApiCalls() {
    console.log('开始执行自动API调用...');

    // 第一步：调用 get_recharge_templates.php
    const url1 = 'modules/get_recharge_templates.php?fetchAll=1';
    console.log('调用URL1:', url1);

    fetch(url1)
        .then(response => {
            console.log('充值模板API响应状态:', response.status);
            if (!response.ok) {
                throw new Error('HTTP error! status: ' + response.status);
            }
            return response.text(); // 先获取文本，然后解析JSON
        })
        .then(text => {
            console.log('充值模板API原始响应:', text);
            try {
                const data = JSON.parse(text);
                console.log('充值模板API调用成功:', data);

                // 等待1.5秒后调用第二个API
                setTimeout(function() {
                    const url2 = 'modules/get_return_rules.php?fetchAll=1';
                    console.log('调用URL2:', url2);

                    // 第二步：调用 get_return_rules.php
                    fetch(url2)
                        .then(response => {
                            console.log('回传规则API响应状态:', response.status);
                            if (!response.ok) {
                                throw new Error('HTTP error! status: ' + response.status);
                            }
                            return response.text();
                        })
                        .then(text => {
                            console.log('回传规则API原始响应:', text);
                            try {
                                const data = JSON.parse(text);
                                console.log('回传规则API调用成功:', data);
                                console.log('自动API调用完成');
                            } catch (e) {
                                console.error('回传规则API JSON解析失败:', e, '原始响应:', text);
                            }
                        })
                        .catch(error => {
                            console.error('回传规则API调用失败:', error);
                        });
                }, 1500); // 1.5秒延迟
            } catch (e) {
                console.error('充值模板API JSON解析失败:', e, '原始响应:', text);
            }
        })
        .catch(error => {
            console.error('充值模板API调用失败:', error);
        });
}
</script>
AUTOLOAD_JS;

}
?>
