<?php
// verify_chapter_fix.php - 验证章节序号默认值修复

echo "验证章节序号默认值修复\n";
echo "========================\n\n";

// 测试函数
function testChapterDefault($bookNames, $bookIds, $chapterNumbers, $testName) {
    echo "测试: $testName\n";
    echo "输入链接名称: " . str_replace("\n", "\\n", $bookNames) . "\n";
    echo "输入书籍ID: " . str_replace("\n", "\\n", $bookIds) . "\n";
    echo "输入章节序号: " . str_replace("\n", "\\n", $chapterNumbers) . "\n";
    
    // 模拟主页面逻辑
    $bookNames = mb_convert_encoding($bookNames, 'UTF-8', mb_detect_encoding($bookNames, 'UTF-8, GBK, GB2312, BIG5', true));
    
    $names = array_filter(array_map(function($item) {
        $trimmed = trim($item);
        return mb_convert_encoding($trimmed, 'UTF-8', mb_detect_encoding($trimmed, 'UTF-8, GBK, GB2312, BIG5', true));
    }, explode("\n", $bookNames)));
    
    $validNames = array_filter($names, function($name) {
        return !empty($name) && mb_check_encoding($name, 'UTF-8');
    });
    
    if (empty($validNames)) {
        echo "❌ 错误: 链接名称为空\n\n";
        return;
    }
    
    $nameCount = count($validNames);
    echo "有效链接名称数量: $nameCount\n";
    
    // 应用修复逻辑
    if (empty($bookIds)) {
        $bookIds = str_repeat("1\n", $nameCount - 1) . "1";
        echo "✅ 自动生成 book_ids\n";
    } else {
        $userBookIds = array_map('trim', explode("\n", $bookIds));
        $userBookIdsCount = count(array_filter($userBookIds, function($id) { return !empty($id); }));
        if ($userBookIdsCount < $nameCount) {
            $needMore = $nameCount - $userBookIdsCount;
            $bookIds .= "\n" . str_repeat("1\n", $needMore - 1) . "1";
            echo "🔧 补充 book_ids: 补充了 $needMore 行\n";
        }
    }
    
    if (empty($chapterNumbers)) {
        $chapterNumbers = str_repeat("1\n", $nameCount - 1) . "1";
        echo "✅ 自动生成 chapter_numbers\n";
    } else {
        $userChapterNumbers = array_map('trim', explode("\n", $chapterNumbers));
        $userChapterNumbersCount = count(array_filter($userChapterNumbers, function($ch) { return !empty($ch); }));
        if ($userChapterNumbersCount < $nameCount) {
            $needMore = $nameCount - $userChapterNumbersCount;
            $chapterNumbers .= "\n" . str_repeat("1\n", $needMore - 1) . "1";
            echo "🔧 补充 chapter_numbers: 补充了 $needMore 行\n";
        }
    }
    
    // 验证结果
    $ids = array_map('trim', explode("\n", $bookIds));
    $chapters = array_map('trim', explode("\n", $chapterNumbers));
    
    echo "最终结果:\n";
    echo "书籍ID: " . str_replace("\n", "\\n", $bookIds) . "\n";
    echo "章节序号: " . str_replace("\n", "\\n", $chapterNumbers) . "\n";
    
    echo "数据匹配验证:\n";
    $success = true;
    foreach ($validNames as $index => $name) {
        $bookId = $ids[$index] ?? '缺失';
        $chapterNum = $chapters[$index] ?? '缺失';
        $status = ($bookId !== '缺失' && $chapterNum !== '缺失') ? '✅' : '❌';
        echo "  [$index] $name -> 书籍ID: $bookId, 章节: $chapterNum $status\n";
        if ($bookId === '缺失' || $chapterNum === '缺失') {
            $success = false;
        }
    }
    
    echo "测试结果: " . ($success ? "✅ 通过" : "❌ 失败") . "\n";
    echo "----------------------------------------\n\n";
    
    return $success;
}

// 执行测试
$allPassed = true;

// 测试1: 完全空白
$allPassed &= testChapterDefault(
    "测试名称1\n测试名称2\n测试名称3",
    "",
    "",
    "完全空白 - 应该自动生成所有默认值"
);

// 测试2: 部分空白
$allPassed &= testChapterDefault(
    "测试名称1\n测试名称2\n测试名称3",
    "100",
    "",
    "书籍ID部分填写，章节序号空白"
);

// 测试3: 行数不足
$allPassed &= testChapterDefault(
    "测试名称1\n测试名称2\n测试名称3\n测试名称4\n测试名称5",
    "100\n200",
    "1\n2\n3",
    "行数不足 - 应该自动补充"
);

// 测试4: 完全匹配
$allPassed &= testChapterDefault(
    "测试名称1\n测试名称2",
    "100\n200",
    "1\n2",
    "完全匹配 - 不应该修改"
);

// 测试5: 单行测试
$allPassed &= testChapterDefault(
    "单个测试名称",
    "",
    "",
    "单行测试"
);

echo "=========================\n";
echo "总体测试结果: " . ($allPassed ? "✅ 所有测试通过" : "❌ 部分测试失败") . "\n";
echo "=========================\n";
?>
