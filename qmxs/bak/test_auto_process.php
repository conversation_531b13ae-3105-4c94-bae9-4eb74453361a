<?php
require_once 'config.php';
require_once 'classes/TaskQueue.php';

echo "<!DOCTYPE html>";
echo "<html><head><meta charset='UTF-8'><title>自动处理测试</title>";
echo "<style>body{font-family:Arial,sans-serif;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;}</style>";
echo "</head><body>";
echo "<h1>自动处理测试</h1>";

try {
    // 连接数据库
    $conn = new mysqli(DB_HOST, DB_USER, DB_PASSWORD, DB_NAME);
    if ($conn->connect_error) {
        throw new Exception("连接失败: " . $conn->connect_error);
    }
    $conn->set_charset("utf8mb4");

    // 清理之前的测试数据
    $conn->query("DELETE FROM create_promotion_link WHERE name LIKE '自动处理测试%'");
    $conn->query("DELETE FROM link WHERE name LIKE '自动处理测试%'");
    echo "<p class='success'>✅ 清理旧测试数据完成</p>";

    // 创建TaskQueue实例
    $taskQueue = new TaskQueue($conn);

    // 准备测试数据
    $testData = [
        [
            'admin_account_name' => 'aishang',
            'account_id' => 553456211129746234,
            'project' => 8,
            'appid' => 'wxe3a874175a6e6ed3',
            'book_id' => 515208,
            'chapter_num' => 1,
            'name' => '自动处理测试A',
            'media_id' => 1,
            'postback_rule_id' => 6505,
            'recharge_panel_id' => 5501,
            'advertiser_account_id' => *********
        ],
        [
            'admin_account_name' => 'aishang',
            'account_id' => 553456211129746234,
            'project' => 8,
            'appid' => 'wxe3a874175a6e6ed3',
            'book_id' => 515208,
            'chapter_num' => 2,
            'name' => '自动处理测试B',
            'media_id' => 1,
            'postback_rule_id' => 6505,
            'recharge_panel_id' => 5501,
            'advertiser_account_id' => *********
        ]
    ];

    // 步骤1：创建任务
    echo "<h2>步骤1：创建任务</h2>";
    $taskId = $taskQueue->createTask($testData);
    echo "<p class='success'>✅ 任务创建成功，任务ID: $taskId</p>";

    // 步骤2：自动触发任务处理（模拟index.php的行为）
    echo "<h2>步骤2：自动触发任务处理</h2>";
    
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
    $baseUrl = $protocol . '://' . $host . dirname($_SERVER['REQUEST_URI']);
    $processorUrl = $baseUrl . '/modules/task_queue_processor.php';
    
    echo "<p><strong>处理器URL:</strong> $processorUrl</p>";
    
    // 调用任务处理器
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $processorUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 120); // 2分钟超时
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    
    echo "<p class='info'>正在处理任务，请稍候...</p>";
    
    $startTime = microtime(true);
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curlError = curl_error($ch);
    curl_close($ch);
    $endTime = microtime(true);
    
    $executionTime = round($endTime - $startTime, 2);
    
    echo "<p><strong>执行时间:</strong> {$executionTime}秒</p>";
    echo "<p><strong>HTTP状态码:</strong> $httpCode</p>";
    
    if ($curlError) {
        echo "<p class='error'><strong>CURL错误:</strong> $curlError</p>";
    } else {
        $result = json_decode($response, true);
        if ($result && $result['code'] === 0) {
            echo "<p class='success'>✅ 任务处理成功</p>";
            $data = $result['data'];
            echo "<ul>";
            echo "<li><strong>总记录数:</strong> " . $data['total_records'] . "</li>";
            echo "<li><strong>成功数量:</strong> " . $data['success'] . "</li>";
            echo "<li><strong>失败数量:</strong> " . $data['failed'] . "</li>";
            echo "</ul>";
        } else {
            echo "<p class='error'>❌ 任务处理失败: " . ($result['msg'] ?? '未知错误') . "</p>";
            echo "<p><strong>响应:</strong> " . htmlspecialchars($response) . "</p>";
        }
    }

    // 步骤3：检查结果
    echo "<h2>步骤3：检查结果</h2>";
    
    // 检查任务状态
    $stmt = $conn->prepare("SELECT id, name, status, error_message FROM create_promotion_link WHERE task_id = ?");
    $stmt->bind_param("s", $taskId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    echo "<h3>任务状态:</h3>";
    echo "<table border='1' style='border-collapse:collapse; width:100%;'>";
    echo "<tr><th>ID</th><th>名称</th><th>状态</th><th>错误信息</th></tr>";
    while ($row = $result->fetch_assoc()) {
        $statusColor = '';
        switch($row['status']) {
            case 'done': $statusColor = 'color:green;'; break;
            case 'failed': $statusColor = 'color:red;'; break;
            case 'running': $statusColor = 'color:blue;'; break;
            case 'pending': $statusColor = 'color:orange;'; break;
        }
        
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . htmlspecialchars($row['name']) . "</td>";
        echo "<td style='$statusColor'>" . $row['status'] . "</td>";
        echo "<td>" . htmlspecialchars($row['error_message'] ?? '') . "</td>";
        echo "</tr>";
    }
    echo "</table>";

    // 检查生成的链接
    $stmt = $conn->prepare("SELECT name, link, advertiser_account_id FROM link WHERE task_id = ?");
    $stmt->bind_param("s", $taskId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        echo "<h3>生成的链接:</h3>";
        echo "<table border='1' style='border-collapse:collapse; width:100%;'>";
        echo "<tr><th>链接名称</th><th>广告主账户ID</th><th>推广链接</th></tr>";
        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($row['name']) . "</td>";
            echo "<td>" . htmlspecialchars($row['advertiser_account_id']) . "</td>";
            echo "<td style='word-break:break-all;'>" . htmlspecialchars($row['link']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<p class='success'>✅ 找到 " . $result->num_rows . " 条生成的链接</p>";
        echo "<p><a href='task_status.php?task_id=$taskId&action=download' target='_blank'>下载Excel文件</a></p>";
    } else {
        echo "<p class='info'>没有找到生成的链接</p>";
    }

    echo "<h2>相关链接</h2>";
    echo "<p><a href='task_status.php?task_id=$taskId' target='_blank'>查看任务状态页面</a></p>";
    echo "<p><a href='index.php' target='_blank'>返回主页面</a></p>";

} catch (Exception $e) {
    echo "<p class='error'>❌ 错误: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "</body></html>";
?>
