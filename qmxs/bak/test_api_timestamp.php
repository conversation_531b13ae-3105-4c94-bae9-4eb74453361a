<?php
// test_api_timestamp.php - 测试API时间戳问题

require_once 'config.php';

// 生成签名函数
function generate_test_signature($params, $secret_key) {
    $signParams = $params;
    unset($signParams['sign']);

    $filteredParams = array_filter($signParams, function($value) {
        return $value !== null && $value !== '';
    });

    ksort($filteredParams);

    $signStr = '';
    foreach ($filteredParams as $key => $value) {
        $signStr .= "{$key}={$value}&";
    }
    $signStr = rtrim($signStr, '&');

    $signStrWithSecret = $signStr . $secret_key;
    return strtolower(md5($signStrWithSecret));
}

echo "测试API时间戳问题\n";
echo "==================\n\n";

// 从数据库获取一条测试数据
try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASSWORD);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    $stmt = $pdo->prepare("SELECT * FROM create_promotion_link ORDER BY id ASC LIMIT 1");
    $stmt->execute();
    $param_row = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$param_row) {
        echo "❌ 没有找到测试数据\n";
        exit;
    }

    echo "✅ 找到测试数据: ID=" . $param_row['id'] . ", 名称=" . $param_row['name'] . "\n\n";

    // 测试不同的时间戳格式
    $testCases = [
        'case1' => [
            'name' => '标准时间戳',
            'timestamp' => time()
        ],
        'case2' => [
            'name' => '字符串时间戳',
            'timestamp' => (string)time()
        ],
        'case3' => [
            'name' => '毫秒时间戳',
            'timestamp' => time() * 1000
        ],
        'case4' => [
            'name' => '无时间戳',
            'timestamp' => null
        ]
    ];

    foreach ($testCases as $caseKey => $testCase) {
        echo "测试 {$testCase['name']} ($caseKey)\n";
        echo "----------------------------------------\n";

        // 构建参数
        $params = [
            'admin_account_name' => $param_row['admin_account_name'],
            'account_id' => $param_row['account_id'],
            'project' => $param_row['project'],
            'appid' => $param_row['appid'],
            'book_id' => $param_row['book_id'],
            'chapter_num' => $param_row['chapter_num'],
            'name' => $param_row['name'],
            'media_id' => $param_row['media_id'],
            'postback_rule_id' => $param_row['postback_rule_id'],
            'recharge_panel_id' => $param_row['recharge_panel_id'],
            'access_key' => ACCESS_KEY,
            'random' => uniqid(),
            'is_platform_postback' => true
        ];

        // 添加时间戳（如果不为null）
        if ($testCase['timestamp'] !== null) {
            $params['timestamp'] = $testCase['timestamp'];
        }

        // 生成签名

        $sign = generate_test_signature($params, SECRET_KEY);
        $params['sign'] = $sign;

        echo "参数: " . json_encode($params, JSON_UNESCAPED_UNICODE) . "\n";

        // 测试POST form-data
        echo "\n测试 POST form-data:\n";
        $ch = curl_init(API_BASE_URL . '/mapi/v2/promotion-link/create');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($params));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/x-www-form-urlencoded'
        ]);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        echo "HTTP状态码: $httpCode\n";
        if ($response) {
            $result = json_decode($response, true);
            if ($result) {
                echo "响应: " . json_encode($result, JSON_UNESCAPED_UNICODE) . "\n";
                if (isset($result['msg'])) {
                    if (strpos($result['msg'], '时间戳') !== false) {
                        echo "❌ 时间戳错误: " . $result['msg'] . "\n";
                    } else if ($result['code'] === 0) {
                        echo "✅ 成功!\n";
                    } else {
                        echo "⚠️ 其他错误: " . $result['msg'] . "\n";
                    }
                }
            } else {
                echo "❌ 响应解析失败: " . substr($response, 0, 100) . "\n";
            }
        } else {
            echo "❌ 无响应\n";
        }

        echo "\n";

        // 测试POST JSON
        echo "测试 POST JSON:\n";
        $ch = curl_init(API_BASE_URL . '/mapi/v2/promotion-link/create');
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($params));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json'
        ]);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        echo "HTTP状态码: $httpCode\n";
        if ($response) {
            $result = json_decode($response, true);
            if ($result) {
                echo "响应: " . json_encode($result, JSON_UNESCAPED_UNICODE) . "\n";
                if (isset($result['msg'])) {
                    if (strpos($result['msg'], '时间戳') !== false) {
                        echo "❌ 时间戳错误: " . $result['msg'] . "\n";
                    } else if ($result['code'] === 0) {
                        echo "✅ 成功!\n";
                        exit; // 如果成功，退出脚本
                    } else {
                        echo "⚠️ 其他错误: " . $result['msg'] . "\n";
                    }
                }
            } else {
                echo "❌ 响应解析失败: " . substr($response, 0, 100) . "\n";
            }
        } else {
            echo "❌ 无响应\n";
        }

        echo "\n" . str_repeat("=", 50) . "\n\n";
    }

} catch (Exception $e) {
    echo "❌ 错误: " . $e->getMessage() . "\n";
}
?>
