#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import re

def fix_id_mapping():
    print("=== 开始修复ID映射问题 ===")
    
    # 读取文件
    with open('index.php', 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("✅ 读取index.php文件")
    
    # 修复1: 充值模板ID查询
    old_pattern1 = r'SELECT template_id FROM recharge_templates WHERE template_name = \?'
    new_pattern1 = 'SELECT template_id FROM recharge_templates WHERE account_name = ? AND template_name = ? LIMIT 1'
    content = re.sub(old_pattern1, new_pattern1, content)
    
    old_bind1 = r'\$stmt->bind_param\("s", \$templateName\);'
    new_bind1 = '$stmt->bind_param("ss", $accountName, $templateName);'
    content = re.sub(old_bind1, new_bind1, content)
    
    print("✅ 修复充值模板ID查询")
    
    # 修复2: 回传规则ID查询
    old_pattern2 = r'SELECT rule_id FROM postback_rules WHERE rule_name = \?'
    new_pattern2 = 'SELECT rule_id FROM postback_rules WHERE account_name = ? AND rule_name = ? LIMIT 1'
    content = re.sub(old_pattern2, new_pattern2, content)
    
    old_bind2 = r'\$stmt->bind_param\("s", \$ruleName\);'
    new_bind2 = '$stmt->bind_param("ss", $accountName, $ruleName);'
    content = re.sub(old_bind2, new_bind2, content)
    
    print("✅ 修复回传规则ID查询")
    
    # 修复3: AppID查询
    old_pattern3 = r'SELECT appid FROM recharge_templates WHERE app_name = \?'
    new_pattern3 = 'SELECT appid FROM recharge_templates WHERE account_name = ? AND app_name = ? LIMIT 1'
    content = re.sub(old_pattern3, new_pattern3, content)
    
    old_bind3 = r'\$stmt->bind_param\("s", \$appName\);'
    new_bind3 = '$stmt->bind_param("ss", $accountName, $appName);'
    content = re.sub(old_bind3, new_bind3, content)
    
    print("✅ 修复AppID查询")
    
    # 添加调试日志
    debug_pattern = r'// 准备任务数据'
    debug_replacement = '''// 添加调试日志
        error_log("ID映射调试信息: " . json_encode([
            "account_name" => $accountName,
            "account_id" => $accountId,
            "template_name" => $templateName,
            "recharge_panel_id" => $rechargePanelId,
            "rule_name" => $ruleName,
            "postback_rule_id" => $postbackRuleId,
            "app_name" => $appName,
            "app_id" => $appId
        ]));

        // 准备任务数据'''
    content = re.sub(debug_pattern, debug_replacement, content)
    
    print("✅ 添加调试日志")
    
    # 写入文件
    with open('index.php', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ 写入修复后的文件")
    print("🎉 修复完成！")

if __name__ == "__main__":
    fix_id_mapping()
