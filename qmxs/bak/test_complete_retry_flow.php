<?php
// test_complete_retry_flow.php - 完整测试重新处理失败任务流程
require_once 'config.php';

// 设置内部编码和页面编码
mb_internal_encoding('UTF-8');
header('Content-Type: text/html; charset=UTF-8');

// 连接数据库
$conn = new mysqli(DB_HOST, DB_USER, DB_PASSWORD, DB_NAME);
if ($conn->connect_error) {
    die("连接失败: " . $conn->connect_error);
}
$conn->set_charset("utf8mb4");

echo "<!DOCTYPE html>
<html>
<head>
    <meta charset='UTF-8'>
    <title>完整测试重新处理失败任务流程</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .step { background: #f9f9f9; padding: 15px; margin: 10px 0; border-left: 4px solid #2196F3; }
        .success { background: #e8f5e8; padding: 10px; border-left: 4px solid #4CAF50; margin: 10px 0; }
        .error { background: #ffebee; padding: 10px; border-left: 4px solid #f44336; margin: 10px 0; }
        .warning { background: #fff3e0; padding: 10px; border-left: 4px solid #ff9800; margin: 10px 0; }
        .info { background: #e7f3ff; padding: 10px; border-left: 4px solid #2196F3; margin: 10px 0; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background: #f2f2f2; }
        .button { padding: 8px 16px; background: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; display: inline-block; margin: 5px; }
        .button:hover { background: #45a049; }
        .button.retry { background: #FF9800; }
        .button.retry:hover { background: #F57C00; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>";

echo "<h1>🧪 完整测试重新处理失败任务流程</h1>";

$taskId = 'task_683fc8865d40d4.01970811_1749010566';

// 步骤1: 检查当前任务状态
echo "<div class='step'>";
echo "<h2>📋 步骤1: 检查当前任务状态</h2>";

$stmt = $conn->prepare("
    SELECT status, COUNT(*) as count, 
           GROUP_CONCAT(error_message SEPARATOR '; ') as error_messages,
           MAX(updated_at) as last_updated
    FROM create_promotion_link 
    WHERE task_id = ?
    GROUP BY status
");
$stmt->bind_param("s", $taskId);
$stmt->execute();
$result = $stmt->get_result();

echo "<table>";
echo "<tr><th>状态</th><th>记录数</th><th>最后更新</th><th>错误信息</th></tr>";
$hasFailed = false;
$hasPending = false;
while ($row = $result->fetch_assoc()) {
    $statusColor = '';
    switch($row['status']) {
        case 'done': $statusColor = 'color:green;'; break;
        case 'failed': $statusColor = 'color:red;'; $hasFailed = true; break;
        case 'running': $statusColor = 'color:blue;'; break;
        case 'pending': $statusColor = 'color:orange;'; $hasPending = true; break;
    }
    
    echo "<tr>";
    echo "<td style='$statusColor'>" . $row['status'] . "</td>";
    echo "<td>" . $row['count'] . "</td>";
    echo "<td>" . $row['last_updated'] . "</td>";
    echo "<td>" . htmlspecialchars($row['error_messages'] ?? '') . "</td>";
    echo "</tr>";
}
echo "</table>";
echo "</div>";

// 步骤2: 模拟重新处理失败任务
if ($hasFailed) {
    echo "<div class='step'>";
    echo "<h2>🔄 步骤2: 模拟重新处理失败任务</h2>";
    
    // 重置失败任务为pending
    $stmt = $conn->prepare("UPDATE create_promotion_link SET status = 'pending', error_message = NULL, updated_at = NOW() WHERE task_id = ? AND status = 'failed'");
    $stmt->bind_param("s", $taskId);
    
    if ($stmt->execute() && $stmt->affected_rows > 0) {
        echo "<p class='success'>✅ 已重置 {$stmt->affected_rows} 条失败记录为pending状态</p>";
        $hasPending = true;
    } else {
        echo "<p class='error'>❌ 重置失败或没有失败记录需要重置</p>";
    }
    echo "</div>";
} else if ($hasPending) {
    echo "<div class='info'>";
    echo "<h2>ℹ️ 任务已经是pending状态，可以直接处理</h2>";
    echo "</div>";
} else {
    echo "<div class='warning'>";
    echo "<h2>⚠️ 任务没有失败记录，无需重新处理</h2>";
    echo "</div>";
}

// 步骤3: 调用任务处理器
if ($hasPending) {
    echo "<div class='step'>";
    echo "<h2>⚙️ 步骤3: 调用任务处理器</h2>";
    
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
    $baseUrl = $protocol . '://' . $host . dirname($_SERVER['REQUEST_URI']);
    $apiUrl = $baseUrl . '/modules/task_queue_processor.php';
    
    echo "<p class='info'>🔗 API URL: $apiUrl</p>";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $apiUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 600);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    
    $startTime = microtime(true);
    $response = curl_exec($ch);
    $endTime = microtime(true);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    $executionTime = round($endTime - $startTime, 2);
    
    echo "<p class='info'>⏱️ 执行时间: {$executionTime} 秒</p>";
    echo "<p class='info'>📊 HTTP状态码: $httpCode</p>";
    
    if ($httpCode == 200) {
        $result = json_decode($response, true);
        
        if ($result && $result['code'] === 0) {
            echo "<div class='success'>";
            echo "<h4>✅ 任务处理成功！</h4>";
            echo "<ul>";
            echo "<li><strong>任务ID:</strong> " . htmlspecialchars($result['data']['task_id']) . "</li>";
            echo "<li><strong>总记录数:</strong> " . $result['data']['total_records'] . "</li>";
            echo "<li><strong>已处理:</strong> " . $result['data']['processed'] . "</li>";
            echo "<li><strong>成功:</strong> " . $result['data']['success'] . "</li>";
            echo "<li><strong>失败:</strong> " . $result['data']['failed'] . "</li>";
            echo "<li><strong>执行时间:</strong> " . $result['data']['execution_time'] . " 秒</li>";
            echo "</ul>";
            echo "</div>";
            
            if (!empty($result['data']['errors'])) {
                echo "<div class='warning'>";
                echo "<h4>⚠️ 错误详情:</h4>";
                echo "<ul>";
                foreach ($result['data']['errors'] as $error) {
                    echo "<li>" . htmlspecialchars($error['error']) . "</li>";
                }
                echo "</ul>";
                echo "</div>";
            }
        } else {
            $errorMsg = $result['msg'] ?? '未知错误';
            echo "<div class='error'>";
            echo "<h4>❌ 任务处理失败</h4>";
            echo "<p><strong>错误信息:</strong> " . htmlspecialchars($errorMsg) . "</p>";
            echo "<details>";
            echo "<summary>查看原始响应</summary>";
            echo "<pre>" . htmlspecialchars($response) . "</pre>";
            echo "</details>";
            echo "</div>";
        }
    } else {
        echo "<div class='error'>";
        echo "<h4>❌ HTTP请求失败</h4>";
        echo "<p><strong>状态码:</strong> $httpCode</p>";
        echo "<details>";
        echo "<summary>查看响应内容</summary>";
        echo "<pre>" . htmlspecialchars($response) . "</pre>";
        echo "</details>";
        echo "</div>";
    }
    echo "</div>";
}

// 步骤4: 查看最终状态
echo "<div class='step'>";
echo "<h2>📊 步骤4: 查看最终状态</h2>";

$stmt = $conn->prepare("
    SELECT status, COUNT(*) as count, 
           GROUP_CONCAT(error_message SEPARATOR '; ') as error_messages,
           MAX(updated_at) as last_updated
    FROM create_promotion_link 
    WHERE task_id = ?
    GROUP BY status
");
$stmt->bind_param("s", $taskId);
$stmt->execute();
$result = $stmt->get_result();

echo "<table>";
echo "<tr><th>状态</th><th>记录数</th><th>最后更新</th><th>错误信息</th></tr>";
while ($row = $result->fetch_assoc()) {
    $statusColor = '';
    switch($row['status']) {
        case 'done': $statusColor = 'color:green;'; break;
        case 'failed': $statusColor = 'color:red;'; break;
        case 'running': $statusColor = 'color:blue;'; break;
        case 'pending': $statusColor = 'color:orange;'; break;
    }
    
    echo "<tr>";
    echo "<td style='$statusColor'>" . $row['status'] . "</td>";
    echo "<td>" . $row['count'] . "</td>";
    echo "<td>" . $row['last_updated'] . "</td>";
    echo "<td>" . htmlspecialchars($row['error_messages'] ?? '') . "</td>";
    echo "</tr>";
}
echo "</table>";
echo "</div>";

// 步骤5: 检查推广链接结果
echo "<div class='step'>";
echo "<h2>🔗 步骤5: 检查推广链接结果</h2>";

$stmt = $conn->prepare("
    SELECT COUNT(*) as link_count
    FROM link 
    WHERE task_id = ?
");
$stmt->bind_param("s", $taskId);
$stmt->execute();
$result = $stmt->get_result();
$row = $result->fetch_assoc();

if ($row['link_count'] > 0) {
    echo "<p class='success'>✅ 找到 {$row['link_count']} 个推广链接结果</p>";
    
    // 显示推广链接详情
    $stmt = $conn->prepare("
        SELECT name, link, advertiser_account_id, created_at
        FROM link 
        WHERE task_id = ?
        ORDER BY created_at ASC
        LIMIT 5
    ");
    $stmt->bind_param("s", $taskId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    echo "<table>";
    echo "<tr><th>链接名称</th><th>推广链接</th><th>广告主账户ID</th><th>创建时间</th></tr>";
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($row['name']) . "</td>";
        echo "<td>" . htmlspecialchars(substr($row['link'], 0, 50)) . "...</td>";
        echo "<td>" . htmlspecialchars($row['advertiser_account_id']) . "</td>";
        echo "<td>" . $row['created_at'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p class='warning'>⚠️ 没有找到推广链接结果</p>";
}
echo "</div>";

// 总结
echo "<div class='step'>";
echo "<h2>🎯 测试总结</h2>";
echo "<p class='success'>✅ 重新处理失败任务功能测试完成！</p>";
echo "<p><strong>任务ID:</strong> " . htmlspecialchars($taskId) . "</p>";
echo "<p><a href='task_status.php?task_id=" . urlencode($taskId) . "' class='button' target='_blank'>查看任务状态页面</a></p>";
echo "<p><a href='index.php' class='button'>返回首页</a></p>";
echo "</div>";

echo "</body></html>";

$conn->close();
?>
