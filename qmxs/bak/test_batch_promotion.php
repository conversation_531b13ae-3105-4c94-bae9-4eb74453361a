<?php
// test_batch_promotion.php - 测试批量创建推广链接功能

// 设置内部编码
mb_internal_encoding('UTF-8');
header('Content-Type: text/html; charset=UTF-8');

require_once 'config.php';

echo "<!DOCTYPE html>";
echo "<html lang='zh-CN'>";
echo "<head><meta charset='UTF-8'><title>批量创建推广链接测试</title></head>";
echo "<body>";
echo "<h1>批量创建推广链接功能测试</h1>";

try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASSWORD);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>1. 检查待处理数据</h2>";
    
    // 查询表中的数据
    $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM create_promotion_link");
    $stmt->execute();
    $count = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    echo "<p>create_promotion_link 表中共有 <strong>$count</strong> 条记录</p>";
    
    if ($count > 0) {
        // 显示所有记录
        $stmt = $pdo->prepare("SELECT id, name, appid, book_id, chapter_num, advertiser_account_id, created_at FROM create_promotion_link ORDER BY id ASC");
        $stmt->execute();
        $records = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h3>待处理记录列表:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
        echo "<tr><th>ID</th><th>名称</th><th>AppID</th><th>书籍ID</th><th>章节序号</th><th>广告主账户ID</th><th>创建时间</th></tr>";
        
        foreach ($records as $record) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($record['id']) . "</td>";
            echo "<td>" . htmlspecialchars($record['name']) . "</td>";
            echo "<td>" . htmlspecialchars($record['appid']) . "</td>";
            echo "<td>" . htmlspecialchars($record['book_id']) . "</td>";
            echo "<td>" . htmlspecialchars($record['chapter_num']) . "</td>";
            echo "<td>" . htmlspecialchars($record['advertiser_account_id']) . "</td>";
            echo "<td>" . htmlspecialchars($record['created_at']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<div style='background-color: #e7f3ff; padding: 15px; border: 1px solid #b3d9ff; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3>🚀 批量处理说明</h3>";
        echo "<ul>";
        echo "<li><strong>处理方式:</strong> 每次处理一条记录，处理完成后删除</li>";
        echo "<li><strong>间隔时间:</strong> 每个请求之间间隔1.5秒</li>";
        echo "<li><strong>API调用:</strong> 使用GET请求，符合接口文档要求</li>";
        echo "<li><strong>签名算法:</strong> 按照接口文档的签名规则</li>";
        echo "<li><strong>预计耗时:</strong> 约 " . (($count - 1) * 1.5) . " 秒（不包括API响应时间）</li>";
        echo "</ul>";
        echo "</div>";
        
        if (isset($_POST['test_single_api'])) {
            echo "<h2>2. 测试单条API调用</h2>";
            echo "<p>调用原始的 create_promotion_link.php 处理一条记录...</p>";
            
            $startTime = microtime(true);
            
            // 调用原始单条处理API
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, "http://localhost/qmxs/modules/create_promotion_link.php");
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 60);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $curlError = curl_error($ch);
            curl_close($ch);
            
            $endTime = microtime(true);
            $executionTime = round($endTime - $startTime, 2);
            
            echo "<h3>单条API调用结果</h3>";
            echo "<p><strong>HTTP状态码:</strong> $httpCode</p>";
            echo "<p><strong>执行时间:</strong> {$executionTime}秒</p>";
            
            if ($curlError) {
                echo "<p style='color: red;'><strong>CURL错误:</strong> $curlError</p>";
            }
            
            if ($response) {
                $result = json_decode($response, true);
                if ($result) {
                    if ($result['code'] === 0) {
                        echo "<div style='background-color: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px; margin: 10px 0;'>";
                        echo "<h4 style='color: #155724; margin: 0 0 10px 0;'>✅ 单条API调用成功！</h4>";
                        echo "<p>这证明原始API是正常工作的，可以进行批量处理。</p>";
                        echo "</div>";
                    } else {
                        echo "<div style='background-color: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px; margin: 10px 0;'>";
                        echo "<h4 style='color: #721c24; margin: 0;'>❌ 单条API调用失败</h4>";
                        echo "<p style='margin: 10px 0 0 0;'><strong>错误:</strong> " . htmlspecialchars($result['msg']) . "</p>";
                        echo "</div>";
                    }
                    
                    echo "<details style='margin: 20px 0;'>";
                    echo "<summary style='cursor: pointer; font-weight: bold;'>查看完整响应数据</summary>";
                    echo "<pre style='background-color: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; border-radius: 5px; overflow-x: auto; font-size: 12px;'>";
                    echo htmlspecialchars(json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
                    echo "</pre>";
                    echo "</details>";
                } else {
                    echo "<p style='color: red;'>❌ 响应解析失败</p>";
                    echo "<pre style='background-color: #f8f9fa; padding: 10px; border: 1px solid #dee2e6; border-radius: 3px; overflow-x: auto; font-size: 11px;'>";
                    echo htmlspecialchars(substr($response, 0, 1000));
                    echo "</pre>";
                }
            } else {
                echo "<p style='color: red;'>❌ API无响应</p>";
            }
            
        } else if (isset($_POST['test_batch_api'])) {
            echo "<h2>2. 测试批量处理</h2>";
            echo "<p>调用 batch_create_promotion_links.php 批量处理所有记录...</p>";
            echo "<p><strong>预计耗时:</strong> 约 " . (($count - 1) * 1.5) . " 秒（不包括API响应时间）</p>";
            
            $beforeCount = $count;
            $startTime = microtime(true);
            
            // 调用批量处理API
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, "http://localhost/qmxs/modules/batch_create_promotion_links.php");
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 600); // 10分钟超时
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $curlError = curl_error($ch);
            curl_close($ch);
            
            $endTime = microtime(true);
            $executionTime = round($endTime - $startTime, 2);
            
            echo "<h3>批量处理结果</h3>";
            echo "<p><strong>HTTP状态码:</strong> $httpCode</p>";
            echo "<p><strong>总执行时间:</strong> {$executionTime}秒</p>";
            
            if ($curlError) {
                echo "<p style='color: red;'><strong>CURL错误:</strong> $curlError</p>";
            }
            
            if ($response) {
                $result = json_decode($response, true);
                if ($result) {
                    if ($result['code'] === 0) {
                        echo "<div style='background-color: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px; margin: 10px 0;'>";
                        echo "<h4 style='color: #155724; margin: 0 0 10px 0;'>✅ 批量处理完成！</h4>";
                        echo "<ul style='margin: 0;'>";
                        echo "<li><strong>总记录数:</strong> " . $result['data']['total_records'] . "</li>";
                        echo "<li><strong>处理数量:</strong> " . $result['data']['processed'] . "</li>";
                        echo "<li><strong>成功数量:</strong> " . $result['data']['success'] . "</li>";
                        echo "<li><strong>失败数量:</strong> " . $result['data']['failed'] . "</li>";
                        echo "<li><strong>平均每条:</strong> " . $result['data']['average_time_per_record'] . "秒</li>";
                        echo "</ul>";
                        echo "</div>";
                        
                        if (!empty($result['data']['results'])) {
                            echo "<h4>✅ 成功创建的链接:</h4>";
                            echo "<ul>";
                            foreach ($result['data']['results'] as $success) {
                                echo "<li>记录ID " . $success['record_id'] . ": " . htmlspecialchars($success['name']);
                                if (isset($success['link_data']['link'])) {
                                    echo " → <a href='" . htmlspecialchars($success['link_data']['link']) . "' target='_blank'>查看链接</a>";
                                }
                                echo "</li>";
                            }
                            echo "</ul>";
                        }
                        
                        if (!empty($result['data']['errors'])) {
                            echo "<h4>❌ 处理失败的记录:</h4>";
                            echo "<ul>";
                            foreach ($result['data']['errors'] as $error) {
                                echo "<li style='color: red;'>记录ID " . $error['record_id'] . " (" . htmlspecialchars($error['name']) . "): " . htmlspecialchars($error['error']) . "</li>";
                            }
                            echo "</ul>";
                        }
                    } else {
                        echo "<div style='background-color: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px; margin: 10px 0;'>";
                        echo "<h4 style='color: #721c24; margin: 0;'>❌ 批量处理失败</h4>";
                        echo "<p style='margin: 10px 0 0 0;'><strong>错误:</strong> " . htmlspecialchars($result['msg']) . "</p>";
                        echo "</div>";
                    }
                    
                    echo "<details style='margin: 20px 0;'>";
                    echo "<summary style='cursor: pointer; font-weight: bold;'>查看完整响应数据</summary>";
                    echo "<pre style='background-color: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; border-radius: 5px; overflow-x: auto; font-size: 12px;'>";
                    echo htmlspecialchars(json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
                    echo "</pre>";
                    echo "</details>";
                } else {
                    echo "<p style='color: red;'>❌ 响应解析失败</p>";
                    echo "<pre style='background-color: #f8f9fa; padding: 10px; border: 1px solid #dee2e6; border-radius: 3px; overflow-x: auto; font-size: 11px;'>";
                    echo htmlspecialchars(substr($response, 0, 1000));
                    echo "</pre>";
                }
            } else {
                echo "<p style='color: red;'>❌ API无响应</p>";
            }
            
            // 检查最终记录数量
            $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM create_promotion_link");
            $stmt->execute();
            $afterCount = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
            
            echo "<h3>数据变化</h3>";
            echo "<p><strong>处理前:</strong> $beforeCount 条记录</p>";
            echo "<p><strong>处理后:</strong> $afterCount 条记录</p>";
            echo "<p><strong>成功处理:</strong> " . ($beforeCount - $afterCount) . " 条记录</p>";
            
        } else {
            echo "<h2>2. 选择测试方式</h2>";
            echo "<form method='post' style='margin: 20px 0;'>";
            echo "<div style='margin: 10px 0;'>";
            echo "<button type='submit' name='test_single_api' style='padding: 15px 30px; background-color: #007bff; color: white; border: none; cursor: pointer; font-size: 16px; border-radius: 5px; margin-right: 10px;'>🔍 测试单条API</button>";
            echo "<span style='color: #666;'>（验证原始API是否正常工作）</span>";
            echo "</div>";
            echo "<div style='margin: 10px 0;'>";
            echo "<button type='submit' name='test_batch_api' style='padding: 15px 30px; background-color: #28a745; color: white; border: none; cursor: pointer; font-size: 16px; border-radius: 5px; margin-right: 10px;'>🚀 测试批量处理</button>";
            echo "<span style='color: #666;'>（批量处理所有记录，1.5秒间隔）</span>";
            echo "</div>";
            echo "</form>";
            
            echo "<div style='background-color: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; border-radius: 5px; margin: 10px 0;'>";
            echo "<p><strong>⚠️ 注意：</strong></p>";
            echo "<ul>";
            echo "<li>建议先测试单条API，确认正常后再进行批量处理</li>";
            echo "<li>批量处理会按照1.5秒间隔依次处理每条记录</li>";
            echo "<li>处理成功的记录会自动从表中删除</li>";
            echo "<li>请耐心等待，不要刷新页面</li>";
            echo "</ul>";
            echo "</div>";
        }
        
    } else {
        echo "<p style='color: orange;'>⚠️ create_promotion_link 表中没有数据，请先通过主页面添加一些推广链接参数。</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ 错误: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<h2>接口文档要点</h2>";
echo "<div style='background-color: #e7f3ff; padding: 15px; border: 1px solid #b3d9ff; border-radius: 5px;'>";
echo "<h3>根据接口文档的要求:</h3>";
echo "<ul>";
echo "<li><strong>签名规则:</strong> Query参数按字典序排序 + secret_key 做MD5计算</li>";
echo "<li><strong>必要参数:</strong> access_key, random, timestamp, sign</li>";
echo "<li><strong>频控限制:</strong> 1次/秒，超限返回码110001</li>";
echo "<li><strong>时效要求:</strong> timestamp需在10分钟内，过期返回码110002</li>";
echo "<li><strong>请求方式:</strong> GET请求，参数放在Query中</li>";
echo "</ul>";
echo "</div>";

echo "</body></html>";
?>
