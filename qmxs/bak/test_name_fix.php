<?php
require_once 'config.php';
require_once 'classes/TaskQueue.php';

echo "<!DOCTYPE html>";
echo "<html><head><meta charset='UTF-8'><title>测试链接名称修复</title></head><body>";
echo "<h1>测试链接名称修复</h1>";

try {
    // 连接数据库
    $conn = new mysqli(DB_HOST, DB_USER, DB_PASSWORD, DB_NAME);
    if ($conn->connect_error) {
        throw new Exception("连接失败: " . $conn->connect_error);
    }
    $conn->set_charset("utf8mb4");

    // 清理之前的测试数据
    $conn->query("DELETE FROM create_promotion_link WHERE name LIKE '测试名称修复%'");
    $conn->query("DELETE FROM link WHERE name LIKE '测试名称修复%'");
    echo "<p>✅ 清理旧测试数据完成</p>";

    // 创建TaskQueue实例
    $taskQueue = new TaskQueue($conn);

    // 准备测试数据
    $testData = [
        [
            'admin_account_name' => 'aishang',
            'account_id' => 553456211129746234,
            'project' => 8,
            'appid' => 'wxe3a874175a6e6ed3',
            'book_id' => 515208,
            'chapter_num' => 1,
            'name' => '测试名称修复A',
            'media_id' => 1,
            'postback_rule_id' => 6505,
            'recharge_panel_id' => 5501,
            'advertiser_account_id' => *********
        ],
        [
            'admin_account_name' => 'aishang',
            'account_id' => 553456211129746234,
            'project' => 8,
            'appid' => 'wxe3a874175a6e6ed3',
            'book_id' => 515208,
            'chapter_num' => 2,
            'name' => '测试名称修复B',
            'media_id' => 1,
            'postback_rule_id' => 6505,
            'recharge_panel_id' => 5501,
            'advertiser_account_id' => *********
        ]
    ];

    // 创建任务
    $taskId = $taskQueue->createTask($testData);
    echo "<p>✅ 任务创建成功，任务ID: $taskId</p>";

    // 验证数据是否正确插入
    $stmt = $conn->prepare("SELECT id, name, advertiser_account_id FROM create_promotion_link WHERE task_id = ?");
    $stmt->bind_param("s", $taskId);
    $stmt->execute();
    $result = $stmt->get_result();

    echo "<h2>插入的数据验证：</h2>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>ID</th><th>链接名称</th><th>广告主账户ID</th></tr>";
    
    $insertedCorrectly = true;
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . $row['name'] . "</td>";
        echo "<td>" . $row['advertiser_account_id'] . "</td>";
        echo "</tr>";
        
        // 检查名称是否正确
        if ($row['name'] === '0' || empty($row['name'])) {
            $insertedCorrectly = false;
        }
    }
    echo "</table>";

    if ($insertedCorrectly) {
        echo "<p style='color: green;'>✅ 链接名称插入正确！</p>";
    } else {
        echo "<p style='color: red;'>❌ 链接名称插入仍有问题</p>";
    }

    // 提供任务状态查询链接
    echo "<p><a href='task_status.php?task_id=$taskId' target='_blank'>查看任务状态</a></p>";

} catch (Exception $e) {
    echo "<p style='color: red;'>❌ 错误: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "</body></html>";
?>
