<?php
// debug_encoding.php - 诊断中英文字符存储问题

// 设置内部编码
mb_internal_encoding('UTF-8');
header('Content-Type: text/html; charset=UTF-8');

require_once 'config.php';

echo "<!DOCTYPE html>";
echo "<html lang='zh-CN'>";
echo "<head><meta charset='UTF-8'><title>编码诊断</title></head>";
echo "<body>";
echo "<h1>中英文字符存储诊断</h1>";

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $testName = $_POST['test_name'] ?? '';
    
    echo "<h2>1. 输入数据分析</h2>";
    echo "原始输入: " . htmlspecialchars($testName) . "<br>";
    echo "字符长度: " . mb_strlen($testName) . "<br>";
    echo "字节长度: " . strlen($testName) . "<br>";
    echo "检测编码: " . mb_detect_encoding($testName, ['UTF-8', 'ASCII', 'GBK', 'GB2312']) . "<br>";
    echo "是否UTF-8: " . (mb_check_encoding($testName, 'UTF-8') ? '是' : '否') . "<br>";
    
    // 模拟主页面的处理逻辑
    echo "<h2>2. 模拟主页面处理逻辑</h2>";
    
    // 按照主页面的方式处理
    $bookNames = $testName;
    $bookNames = mb_convert_encoding($bookNames, 'UTF-8', mb_detect_encoding($bookNames, 'UTF-8, GBK, GB2312, BIG5', true));
    
    echo "经过 mb_convert_encoding 处理: " . htmlspecialchars($bookNames) . "<br>";
    
    // 按照主页面的方式分割和过滤
    $names = array_filter(array_map(function($item) {
        $trimmed = trim($item);
        return mb_convert_encoding($trimmed, 'UTF-8', mb_detect_encoding($trimmed, 'UTF-8, GBK, GB2312, BIG5', true));
    }, explode("\n", $bookNames)));
    
    echo "分割后的数组: ";
    var_dump($names);
    echo "<br>";
    
    // 验证过滤
    $validNames = array_filter($names, function($name) {
        return !empty($name) && mb_check_encoding($name, 'UTF-8');
    });
    
    echo "验证后的数组: ";
    var_dump($validNames);
    echo "<br>";
    
    if (!empty($validNames)) {
        echo "<h2>3. 数据库测试</h2>";
        
        try {
            $conn = new mysqli(DB_HOST, DB_USER, DB_PASSWORD, DB_NAME);
            if ($conn->connect_error) {
                throw new Exception("连接失败: " . $conn->connect_error);
            }
            
            // 设置字符集
            $conn->set_charset("utf8mb4");
            $conn->query("SET NAMES 'utf8mb4'");
            
            echo "数据库连接成功，字符集设置为 utf8mb4<br>";
            
            // 检查当前连接的字符集
            $result = $conn->query("SELECT @@character_set_connection, @@collation_connection");
            $row = $result->fetch_row();
            echo "当前连接字符集: {$row[0]}, 排序规则: {$row[1]}<br>";
            
            // 准备插入语句（简化版本）
            $stmt = $conn->prepare("INSERT INTO create_promotion_link (
                admin_account_name, account_id, project, appid, 
                book_id, chapter_num, name, media_id, 
                postback_rule_id, recharge_panel_id, advertiser_account_id
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
            
            if (!$stmt) {
                throw new Exception("准备语句失败: " . $conn->error);
            }
            
            foreach ($validNames as $name) {
                echo "<h3>插入测试: " . htmlspecialchars($name) . "</h3>";
                
                // 设置测试数据
                $adminAccountName = "test_admin";
                $accountId = 1;
                $project = 8;
                $appId = "test_app";
                $bookId = 1;
                $chapterNum = 1;
                $mediaId = 1;
                $postbackRuleId = 1;
                $rechargePanelId = 1;
                $advertiserId = 1;
                
                echo "绑定参数类型: siisiiisiii<br>";
                echo "参数值: ";
                var_dump([
                    $adminAccountName, $accountId, $project, $appId,
                    $bookId, $chapterNum, $name, $mediaId,
                    $postbackRuleId, $rechargePanelId, $advertiserId
                ]);
                echo "<br>";
                
                $stmt->bind_param(
                    "siisiiisiii",
                    $adminAccountName, $accountId, $project, $appId,
                    $bookId, $chapterNum, $name, $mediaId,
                    $postbackRuleId, $rechargePanelId, $advertiserId
                );
                
                if ($stmt->execute()) {
                    $insertId = $conn->insert_id;
                    echo "✅ 插入成功，ID: $insertId<br>";
                    
                    // 立即查询验证
                    $checkStmt = $conn->prepare("SELECT name FROM create_promotion_link WHERE id = ?");
                    $checkStmt->bind_param("i", $insertId);
                    $checkStmt->execute();
                    $result = $checkStmt->get_result();
                    $row = $result->fetch_assoc();
                    
                    echo "数据库中存储的值: " . htmlspecialchars($row['name']) . "<br>";
                    echo "存储值字节长度: " . strlen($row['name']) . "<br>";
                    echo "存储值字符长度: " . mb_strlen($row['name']) . "<br>";
                    
                } else {
                    echo "❌ 插入失败: " . $stmt->error . "<br>";
                }
            }
            
        } catch (Exception $e) {
            echo "❌ 错误: " . $e->getMessage() . "<br>";
        }
    } else {
        echo "<h2>❌ 没有有效的名称数据</h2>";
    }
}

echo "<h2>测试表单</h2>";
echo "<form method='post'>";
echo "<label>输入测试字符（支持中英文）：</label><br>";
echo "<textarea name='test_name' rows='3' cols='50' placeholder='请输入测试内容，支持多行'>" . htmlspecialchars($_POST['test_name'] ?? '') . "</textarea><br><br>";
echo "<button type='submit'>开始诊断</button>";
echo "</form>";

echo "</body></html>";
?>
