<?php
require_once 'config.php';
require_once 'classes/TaskQueue.php';

echo "<!DOCTYPE html>";
echo "<html><head><meta charset='UTF-8'><title>任务处理器测试</title>";
echo "<style>body{font-family:Arial,sans-serif;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;} pre{background:#f5f5f5;padding:10px;border:1px solid #ddd;}</style>";
echo "</head><body>";
echo "<h1>任务处理器测试</h1>";

try {
    // 连接数据库
    $conn = new mysqli(DB_HOST, DB_USER, DB_PASSWORD, DB_NAME);
    if ($conn->connect_error) {
        throw new Exception("连接失败: " . $conn->connect_error);
    }
    $conn->set_charset("utf8mb4");

    // 创建TaskQueue实例
    $taskQueue = new TaskQueue($conn);

    // 查找失败的任务
    $stmt = $conn->prepare("SELECT task_id FROM create_promotion_link WHERE status = 'failed' GROUP BY task_id LIMIT 1");
    $stmt->execute();
    $result = $stmt->get_result();
    $failedTask = $result->fetch_assoc();

    if ($failedTask) {
        $taskId = $failedTask['task_id'];
        echo "<p class='info'>找到失败任务: $taskId</p>";

        // 重置任务状态为pending
        $stmt = $conn->prepare("UPDATE create_promotion_link SET status = 'pending', error_message = NULL WHERE task_id = ?");
        $stmt->bind_param("s", $taskId);
        $stmt->execute();
        echo "<p class='success'>✅ 已重置任务状态为pending</p>";

        // 手动调用任务处理器
        echo "<h2>调用任务处理器</h2>";
        
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
        $baseUrl = $protocol . '://' . $host . dirname($_SERVER['REQUEST_URI']);
        $apiUrl = $baseUrl . '/modules/task_queue_processor.php';
        
        echo "<p>API URL: $apiUrl</p>";
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $apiUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 120);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        
        $startTime = microtime(true);
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curlError = curl_error($ch);
        curl_close($ch);
        $endTime = microtime(true);
        
        $executionTime = round($endTime - $startTime, 2);
        
        echo "<p><strong>执行时间:</strong> {$executionTime}秒</p>";
        echo "<p><strong>HTTP状态码:</strong> $httpCode</p>";
        
        if ($curlError) {
            echo "<p class='error'>CURL错误: $curlError</p>";
        } else {
            echo "<h3>API响应:</h3>";
            echo "<pre>" . htmlspecialchars($response) . "</pre>";
            
            $result = json_decode($response, true);
            if ($result) {
                echo "<h3>解析后的响应:</h3>";
                echo "<pre>" . print_r($result, true) . "</pre>";
                
                if ($result['code'] === 0) {
                    echo "<p class='success'>✅ 任务处理成功</p>";
                } else {
                    echo "<p class='error'>❌ 任务处理失败: " . ($result['msg'] ?? '未知错误') . "</p>";
                }
            }
        }

        // 检查任务状态
        echo "<h2>任务状态检查</h2>";
        $stmt = $conn->prepare("SELECT id, name, status, error_message FROM create_promotion_link WHERE task_id = ?");
        $stmt->bind_param("s", $taskId);
        $stmt->execute();
        $result = $stmt->get_result();
        
        echo "<table border='1' style='border-collapse:collapse;'>";
        echo "<tr><th>ID</th><th>名称</th><th>状态</th><th>错误信息</th></tr>";
        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $row['id'] . "</td>";
            echo "<td>" . htmlspecialchars($row['name']) . "</td>";
            echo "<td>" . $row['status'] . "</td>";
            echo "<td>" . htmlspecialchars($row['error_message'] ?? '') . "</td>";
            echo "</tr>";
        }
        echo "</table>";

    } else {
        echo "<p class='info'>没有找到失败的任务</p>";
        
        // 创建一个新的测试任务
        echo "<h2>创建新的测试任务</h2>";
        
        $testData = [
            [
                'admin_account_name' => 'aishang',
                'account_id' => 553456211129746234,
                'project' => 8,
                'appid' => 'wxe3a874175a6e6ed3',
                'book_id' => 515208,
                'chapter_num' => 1,
                'name' => '处理器测试任务',
                'media_id' => 1,
                'postback_rule_id' => 6505,
                'recharge_panel_id' => 5501,
                'advertiser_account_id' => *********
            ]
        ];

        $taskId = $taskQueue->createTask($testData);
        echo "<p class='success'>✅ 新任务创建成功，任务ID: $taskId</p>";
        echo "<p><a href='?retry=1'>点击重新运行测试</a></p>";
    }

} catch (Exception $e) {
    echo "<p class='error'>❌ 错误: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "</body></html>";
?>
