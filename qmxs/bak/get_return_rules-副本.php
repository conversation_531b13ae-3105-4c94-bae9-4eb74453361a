<?php
// 在文件顶部新增强制UTF-8编码声明
header('Content-Type: application/json; charset=utf-8');
// get_return_rules.php - 获取回传规则列表

// 引入配置文件
require_once '../config.php';

// 设置API请求参数
$params = [
    'admin_account_name' => isset($_GET['admin_account_name']) ? $_GET['admin_account_name'] : ADMIN_ACCOUNT_NAME,
    'project' => isset($_GET['project']) ? $_GET['project'] : PROJECT_ID,
    'page' => isset($_GET['page']) ? $_GET['page'] : 1,
    'page_size' => isset($_GET['page_size']) ? $_GET['page_size'] : 100,
    'access_key' => ACCESS_KEY, // 使用配置中的access_key
    'random' => rand(), // 随机数
    'timestamp' => time(), // 当前时间戳
];

// 生成签名的逻辑
$sign_params = $params;
unset($sign_params['sign']); // 排除sign字段

// 新增过滤空值和null值的逻辑
$filtered_params = array_filter($sign_params, function($value) {
    return $value !== null && $value !== '';
});

ksort($filtered_params); // 按键排序
$sign_str = '';
foreach ($filtered_params as $key => $val) {
    $sign_str .= "{$key}={$val}&"; // 保持原始格式拼接
}
$sign_str = rtrim($sign_str, '&');
$sign = strtolower(md5($sign_str . SECRET_KEY)); // 使用MD5算法并拼接密钥
$params['sign'] = $sign;

// 构造完整的API请求URL
$api_url = API_BASE_URL . '/mapi/v1/postback-rule/list';
$query_string = http_build_query($params);
$full_api_url = $api_url . '?' . $query_string;

// 发送GET请求
$ch = curl_init($full_api_url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
$response = curl_exec($ch);
curl_close($ch);

// 解析JSON响应
$result = json_decode($response, true);

if ($result['code'] === 0) {
    // 获取数据库连接
    $conn = new mysqli(DB_HOST, DB_USER, DB_PASSWORD, DB_NAME);
    if ($conn->connect_error) {
        die("连接失败: " . $conn->connect_error);
    }

    try {
        // 设置字符集
        $conn->set_charset("utf8mb4");

        // 准备插入语句
        $insert_sql = "INSERT INTO postback_rules
                      (account_id, account_name, project, appid, app_name, rule_id, rule_name, media_id)
                      VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
        $stmt = $conn->prepare($insert_sql);
        if (!$stmt) {
            throw new Exception("Prepare failed: " . $conn->error);
        }

        foreach ($result['data']['list'] as $rule) {
            // 新增过滤逻辑：跳过含有"JS:"前缀的account_id
            if (strpos($rule['account_id'], 'JS:') === 0) {
                continue;
            }

            // 过滤含有"JS:"前缀的字段
            $skip = false;
            foreach ($rule as $key => $value) {
                if (is_string($value) && strpos($value, 'JS:') === 0) {
                    $skip = true;
                    break;
                }
            }
            if ($skip) continue;

            // 参数类型转换（确保所有参数类型正确）
            $accountId = intval($rule['account_id']);
            $accountName = (string)$rule['account_name'];
            $projectId = (string)$rule['project'];
            $appId = (string)$rule['appid'];
            $appName = (string)$rule['app_name'];
            $ruleId = intval($rule['rule_id']);
            $ruleName = (string)$rule['rule_name'];
            $mediaId = intval($rule['media_id']);

            // 绑定参数并执行
            $stmt->bind_param(
                "issssssi", // 修正类型定义：i=integer, s=string
                $accountId,
                $accountName,
                $projectId,
                $appId,
                $appName,
                $ruleId,
                $ruleName,
                $mediaId
            );

            if (!$stmt->execute()) {
                // 记录完整错误信息
                error_log("回传规则插入错误: " . $stmt->error . " 完整参数: " . json_encode([
                    'account_id' => $accountId,
                    'account_name' => $accountName,
                    'project' => $projectId,
                    'appid' => $appId,
                    'app_name' => $appName,
                    'rule_id' => $ruleId,
                    'rule_name' => $ruleName,
                    'media_id' => $mediaId
                ]));
                continue;
            }
        }
    } catch (Exception $e) {
        error_log("回传规则写入异常: " . $e->getMessage());
    }

    $responseData = [
        'list' => [],
        'pagination' => []
    ];

    $rules = $result['data']['list'];
    $pagination = $result['data']['pagination'];

    if (!empty($rules)) {
        foreach ($rules as $rule) {
            // 新增过滤逻辑：跳过含有"JS:"前缀的account_id
            if (strpos($rule['account_id'], 'JS:') === 0) {
                continue;
            }

            // 确保所有字段类型正确
            $accountId = (int)$rule['account_id'];
            $accountName = (string)$rule['account_name'];
            $project = (int)$rule['project'];
            $appId = (string)$rule['appid'];
            $appName = (string)$rule['app_name'];
            $ruleId = (int)$rule['rule_id'];
            $ruleName = (string)$rule['rule_name'];
            $mediaId = (int)$rule['media_id'];

            // 构建完整的PostbackRule对象
            $postbackRule = [
                'account_id' => $accountId,
                'account_name' => $accountName,
                'project' => $project,
                'appid' => $appId,
                'app_name' => $appName,
                'rule_id' => $ruleId,
                'rule_name' => $ruleName,
                'media_id' => $mediaId
            ];

            $responseData['list'][] = $postbackRule;
        }
    }

    // 添加分页信息
    $responseData['pagination'] = [
        'page' => $pagination['page'],
        'page_size' => $pagination['page_size'],
        'total_count' => $pagination['total_count']
    ];

    // 统一响应结构
    $finalResponse = [
        'code' => 0,
        'msg' => $result['msg'] ?? 'success',
        'data' => $responseData,
        'request_id' => uniqid('req-', true)
    ];

    $output = json_encode($finalResponse, JSON_UNESCAPED_UNICODE | JSON_NUMERIC_CHECK);
    echo $output;
    exit; // 新增立即退出防止后续输出
} else {
    // 错误响应保持原有结构
    $errorResponse = [
        'code' => $result['code'] ?? -1,
        'msg' => $result['msg'] ?? 'API调用失败',
        'data' => null,
        'request_id' => uniqid('req-', true)
    ];

    $errorOutput = json_encode($errorResponse, JSON_UNESCAPED_UNICODE | JSON_NUMERIC_CHECK);
    echo $errorOutput;
    exit; // 新增立即退出防止后续输出
}
