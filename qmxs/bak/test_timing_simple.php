<?php
// test_timing_simple.php - 简单测试1.5秒间隔

echo "测试1.5秒间隔功能\n";
echo "==================\n\n";

// 模拟批量处理的间隔逻辑
$recordCount = 3; // 假设有3条记录

echo "开始模拟处理 $recordCount 条记录...\n";
$startTime = microtime(true);

for ($i = 1; $i <= $recordCount; $i++) {
    $currentTime = microtime(true);
    $elapsed = round($currentTime - $startTime, 2);
    
    echo "处理记录 $i - 时间: " . date('H:i:s') . " (已耗时: {$elapsed}秒)\n";
    
    // 除了最后一条记录，其他都需要等待间隔
    if ($i < $recordCount) {
        echo "等待1.5秒间隔...\n";
        usleep(1500000); // 1.5秒 = 1500000微秒
    }
}

$endTime = microtime(true);
$totalTime = round($endTime - $startTime, 2);

echo "\n处理完成！\n";
echo "总耗时: {$totalTime}秒\n";
echo "预期间隔时间: " . (($recordCount - 1) * 1.5) . "秒\n";
echo "平均每条记录: " . round($totalTime / $recordCount, 2) . "秒\n";

// 验证间隔时间是否正确
$expectedTime = ($recordCount - 1) * 1.5;
$tolerance = 0.1; // 允许0.1秒的误差

if (abs($totalTime - $expectedTime) <= $tolerance) {
    echo "✅ 间隔时间控制正确！\n";
} else {
    echo "⚠️ 间隔时间可能有偏差\n";
}
?>
