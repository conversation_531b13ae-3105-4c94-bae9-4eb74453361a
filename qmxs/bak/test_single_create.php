<?php
// test_single_create.php - 测试单条记录处理逻辑

// 设置内部编码
mb_internal_encoding('UTF-8');
header('Content-Type: text/html; charset=UTF-8');

require_once 'config.php';

echo "<!DOCTYPE html>";
echo "<html lang='zh-CN'>";
echo "<head><meta charset='UTF-8'><title>单条记录处理测试</title></head>";
echo "<body>";
echo "<h1>单条记录处理逻辑测试</h1>";

try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASSWORD);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>1. 检查待处理数据</h2>";
    
    // 查询表中的数据
    $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM create_promotion_link");
    $stmt->execute();
    $count = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    echo "<p>create_promotion_link 表中共有 <strong>$count</strong> 条记录</p>";
    
    if ($count > 0) {
        // 显示所有记录
        $stmt = $pdo->prepare("SELECT id, name, appid, book_id, chapter_num, advertiser_account_id, created_at FROM create_promotion_link ORDER BY id ASC");
        $stmt->execute();
        $records = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h3>待处理记录列表:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
        echo "<tr><th>ID</th><th>名称</th><th>AppID</th><th>书籍ID</th><th>章节序号</th><th>广告主账户ID</th><th>创建时间</th></tr>";
        
        foreach ($records as $record) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($record['id']) . "</td>";
            echo "<td>" . htmlspecialchars($record['name']) . "</td>";
            echo "<td>" . htmlspecialchars($record['appid']) . "</td>";
            echo "<td>" . htmlspecialchars($record['book_id']) . "</td>";
            echo "<td>" . htmlspecialchars($record['chapter_num']) . "</td>";
            echo "<td>" . htmlspecialchars($record['advertiser_account_id']) . "</td>";
            echo "<td>" . htmlspecialchars($record['created_at']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<div style='background-color: #e7f3ff; padding: 15px; border: 1px solid #b3d9ff; border-radius: 5px; margin: 20px 0;'>";
        echo "<h3>🔄 新的处理逻辑</h3>";
        echo "<ul>";
        echo "<li><strong>单条处理:</strong> create_promotion_link.php 现在每次只处理一条记录</li>";
        echo "<li><strong>自动删除:</strong> 处理成功后自动删除该记录</li>";
        echo "<li><strong>批量调度:</strong> 使用 batch_create_links.php 按1.5秒间隔多次调用</li>";
        echo "<li><strong>错误隔离:</strong> 单条记录失败不影响其他记录</li>";
        echo "</ul>";
        echo "</div>";
        
        if (isset($_POST['test_single'])) {
            echo "<h2>2. 测试单条记录处理</h2>";
            echo "<p>调用 create_promotion_link.php 处理一条记录...</p>";
            
            $beforeCount = $count;
            $startTime = microtime(true);
            
            // 调用单条处理API
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, "http://localhost/qmxs/modules/create_promotion_link.php");
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 60);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $curlError = curl_error($ch);
            curl_close($ch);
            
            $endTime = microtime(true);
            $executionTime = round($endTime - $startTime, 2);
            
            echo "<h3>单条处理结果</h3>";
            echo "<p><strong>HTTP状态码:</strong> $httpCode</p>";
            echo "<p><strong>执行时间:</strong> {$executionTime}秒</p>";
            
            if ($curlError) {
                echo "<p style='color: red;'><strong>CURL错误:</strong> $curlError</p>";
            }
            
            if ($response) {
                $result = json_decode($response, true);
                if ($result) {
                    if ($result['code'] === 0) {
                        echo "<div style='background-color: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px; margin: 10px 0;'>";
                        echo "<h4 style='color: #155724; margin: 0 0 10px 0;'>✅ 单条记录处理成功！</h4>";
                        echo "<ul style='margin: 0;'>";
                        echo "<li><strong>记录ID:</strong> " . $result['data']['record_id'] . "</li>";
                        echo "<li><strong>名称:</strong> " . htmlspecialchars($result['data']['name']) . "</li>";
                        echo "<li><strong>状态:</strong> " . $result['data']['status'] . "</li>";
                        if (isset($result['data']['link_data']['link'])) {
                            echo "<li><strong>推广链接:</strong> " . htmlspecialchars($result['data']['link_data']['link']) . "</li>";
                        }
                        echo "</ul>";
                        echo "</div>";
                    } else {
                        echo "<div style='background-color: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px; margin: 10px 0;'>";
                        echo "<h4 style='color: #721c24; margin: 0;'>❌ 处理失败</h4>";
                        echo "<p style='margin: 10px 0 0 0;'><strong>错误:</strong> " . htmlspecialchars($result['msg']) . "</p>";
                        echo "</div>";
                    }
                    
                    echo "<details style='margin: 20px 0;'>";
                    echo "<summary style='cursor: pointer; font-weight: bold;'>查看完整响应数据</summary>";
                    echo "<pre style='background-color: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; border-radius: 5px; overflow-x: auto; font-size: 12px;'>";
                    echo htmlspecialchars(json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
                    echo "</pre>";
                    echo "</details>";
                } else {
                    echo "<p style='color: red;'>❌ 响应解析失败</p>";
                    echo "<pre style='background-color: #f8f9fa; padding: 10px; border: 1px solid #dee2e6; border-radius: 3px; overflow-x: auto; font-size: 11px;'>";
                    echo htmlspecialchars(substr($response, 0, 1000));
                    echo "</pre>";
                }
            } else {
                echo "<p style='color: red;'>❌ API无响应</p>";
            }
            
            // 检查记录数量变化
            $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM create_promotion_link");
            $stmt->execute();
            $afterCount = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
            
            echo "<h3>记录数量变化</h3>";
            echo "<p><strong>处理前:</strong> $beforeCount 条记录</p>";
            echo "<p><strong>处理后:</strong> $afterCount 条记录</p>";
            echo "<p><strong>变化:</strong> " . ($beforeCount - $afterCount) . " 条记录被处理</p>";
            
        } else if (isset($_POST['test_batch'])) {
            echo "<h2>2. 测试批量调度处理</h2>";
            echo "<p>调用 batch_create_links.php 批量处理所有记录...</p>";
            echo "<p><strong>预计耗时:</strong> 约 " . (($count - 1) * 1.5) . " 秒（不包括API响应时间）</p>";
            
            $beforeCount = $count;
            $startTime = microtime(true);
            
            // 调用批量调度器
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, "http://localhost/qmxs/batch_create_links.php");
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 300); // 5分钟超时
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $curlError = curl_error($ch);
            curl_close($ch);
            
            $endTime = microtime(true);
            $executionTime = round($endTime - $startTime, 2);
            
            echo "<h3>批量处理结果</h3>";
            echo "<p><strong>HTTP状态码:</strong> $httpCode</p>";
            echo "<p><strong>总执行时间:</strong> {$executionTime}秒</p>";
            
            if ($curlError) {
                echo "<p style='color: red;'><strong>CURL错误:</strong> $curlError</p>";
            }
            
            if ($response) {
                $result = json_decode($response, true);
                if ($result) {
                    if ($result['code'] === 0) {
                        echo "<div style='background-color: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px; margin: 10px 0;'>";
                        echo "<h4 style='color: #155724; margin: 0 0 10px 0;'>✅ 批量处理完成！</h4>";
                        echo "<ul style='margin: 0;'>";
                        echo "<li><strong>总记录数:</strong> " . $result['data']['total_records'] . "</li>";
                        echo "<li><strong>处理数量:</strong> " . $result['data']['processed'] . "</li>";
                        echo "<li><strong>成功数量:</strong> " . $result['data']['success'] . "</li>";
                        echo "<li><strong>失败数量:</strong> " . $result['data']['failed'] . "</li>";
                        echo "<li><strong>平均每条:</strong> " . $result['data']['average_time_per_record'] . "秒</li>";
                        echo "</ul>";
                        echo "</div>";
                        
                        if (!empty($result['data']['errors'])) {
                            echo "<h4>处理失败的记录:</h4>";
                            echo "<ul>";
                            foreach ($result['data']['errors'] as $error) {
                                echo "<li style='color: red;'>序号 " . $error['sequence'] . ": " . htmlspecialchars($error['error']) . "</li>";
                            }
                            echo "</ul>";
                        }
                    } else {
                        echo "<div style='background-color: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px; margin: 10px 0;'>";
                        echo "<h4 style='color: #721c24; margin: 0;'>❌ 批量处理失败</h4>";
                        echo "<p style='margin: 10px 0 0 0;'><strong>错误:</strong> " . htmlspecialchars($result['msg']) . "</p>";
                        echo "</div>";
                    }
                } else {
                    echo "<p style='color: red;'>❌ 响应解析失败</p>";
                }
            } else {
                echo "<p style='color: red;'>❌ API无响应</p>";
            }
            
            // 检查最终记录数量
            $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM create_promotion_link");
            $stmt->execute();
            $afterCount = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
            
            echo "<h3>最终结果</h3>";
            echo "<p><strong>处理前:</strong> $beforeCount 条记录</p>";
            echo "<p><strong>处理后:</strong> $afterCount 条记录</p>";
            echo "<p><strong>成功处理:</strong> " . ($beforeCount - $afterCount) . " 条记录</p>";
        } else {
            echo "<h2>2. 选择测试方式</h2>";
            echo "<form method='post' style='margin: 20px 0;'>";
            echo "<div style='margin: 10px 0;'>";
            echo "<button type='submit' name='test_single' style='padding: 15px 30px; background-color: #007bff; color: white; border: none; cursor: pointer; font-size: 16px; border-radius: 5px; margin-right: 10px;'>🔍 测试单条处理</button>";
            echo "<span style='color: #666;'>（处理一条记录，验证单条逻辑）</span>";
            echo "</div>";
            echo "<div style='margin: 10px 0;'>";
            echo "<button type='submit' name='test_batch' style='padding: 15px 30px; background-color: #28a745; color: white; border: none; cursor: pointer; font-size: 16px; border-radius: 5px; margin-right: 10px;'>🚀 测试批量处理</button>";
            echo "<span style='color: #666;'>（处理所有记录，1.5秒间隔）</span>";
            echo "</div>";
            echo "</form>";
        }
        
    } else {
        echo "<p style='color: orange;'>⚠️ create_promotion_link 表中没有数据，请先通过主页面添加一些推广链接参数。</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ 错误: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<h2>新架构说明</h2>";
echo "<div style='background-color: #e7f3ff; padding: 15px; border: 1px solid #b3d9ff; border-radius: 5px;'>";
echo "<h3>架构改进:</h3>";
echo "<ul>";
echo "<li><strong>create_promotion_link.php:</strong> 每次只处理一条记录，处理完成后删除</li>";
echo "<li><strong>batch_create_links.php:</strong> 批量调度器，按1.5秒间隔多次调用单条处理</li>";
echo "<li><strong>优势:</strong> 更好的错误隔离、精确的间隔控制、简化的逻辑</li>";
echo "</ul>";
echo "</div>";

echo "</body></html>";
?>
