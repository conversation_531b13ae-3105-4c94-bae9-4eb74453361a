<?php
// test_fixed_api.php - 测试修复后的批量创建推广链接API

// 设置内部编码
mb_internal_encoding('UTF-8');
header('Content-Type: text/html; charset=UTF-8');

require_once 'config.php';

echo "<!DOCTYPE html>";
echo "<html lang='zh-CN'>";
echo "<head><meta charset='UTF-8'><title>修复后的API测试</title></head>";
echo "<body>";
echo "<h1>修复后的批量创建推广链接API测试</h1>";

try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASSWORD);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>1. 检查待处理数据</h2>";
    
    // 查询表中的数据
    $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM create_promotion_link");
    $stmt->execute();
    $count = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    echo "<p>create_promotion_link 表中共有 <strong>$count</strong> 条记录</p>";
    
    if ($count > 0) {
        // 显示记录详情
        $stmt = $pdo->prepare("SELECT id, name, appid, book_id, chapter_num, advertiser_account_id, created_at FROM create_promotion_link ORDER BY id ASC");
        $stmt->execute();
        $records = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<h3>待处理记录:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
        echo "<tr><th>ID</th><th>名称</th><th>AppID</th><th>书籍ID</th><th>章节序号</th><th>广告主账户ID</th><th>创建时间</th></tr>";
        
        foreach ($records as $record) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($record['id']) . "</td>";
            echo "<td>" . htmlspecialchars($record['name']) . "</td>";
            echo "<td>" . htmlspecialchars($record['appid']) . "</td>";
            echo "<td>" . htmlspecialchars($record['book_id']) . "</td>";
            echo "<td>" . htmlspecialchars($record['chapter_num']) . "</td>";
            echo "<td>" . htmlspecialchars($record['advertiser_account_id']) . "</td>";
            echo "<td>" . htmlspecialchars($record['created_at']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        if (isset($_POST['test_fixed_api'])) {
            echo "<h2>2. 执行修复后的API测试</h2>";
            echo "<p>开始调用修复后的 create_promotion_link.php...</p>";
            
            $startTime = microtime(true);
            
            // 调用修复后的API
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, "http://localhost/qmxs/modules/create_promotion_link.php");
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 120);
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $curlError = curl_error($ch);
            curl_close($ch);
            
            $endTime = microtime(true);
            $executionTime = round($endTime - $startTime, 2);
            
            echo "<h3>API调用结果</h3>";
            echo "<p><strong>HTTP状态码:</strong> $httpCode</p>";
            echo "<p><strong>执行时间:</strong> {$executionTime}秒</p>";
            
            if ($curlError) {
                echo "<p style='color: red;'><strong>CURL错误:</strong> $curlError</p>";
            }
            
            if ($response) {
                $result = json_decode($response, true);
                if ($result) {
                    echo "<h3>API响应数据</h3>";
                    
                    if ($result['code'] === 0) {
                        echo "<div style='background-color: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px; margin: 10px 0;'>";
                        echo "<h4 style='color: #155724; margin: 0 0 10px 0;'>✅ 批量处理成功！</h4>";
                        echo "<ul style='margin: 0;'>";
                        echo "<li><strong>总处理数量:</strong> " . $result['data']['total_processed'] . "</li>";
                        echo "<li><strong>成功数量:</strong> " . $result['data']['success_count'] . "</li>";
                        echo "<li><strong>失败数量:</strong> " . $result['data']['fail_count'] . "</li>";
                        echo "</ul>";
                        echo "</div>";
                        
                        // 显示成功的记录
                        if (!empty($result['data']['results'])) {
                            echo "<h4>成功创建的链接:</h4>";
                            echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
                            echo "<tr><th>记录ID</th><th>名称</th><th>链接ID</th><th>推广链接</th><th>监测链接</th></tr>";
                            
                            foreach ($result['data']['results'] as $success) {
                                echo "<tr>";
                                echo "<td>" . htmlspecialchars($success['record_id']) . "</td>";
                                echo "<td>" . htmlspecialchars($success['name']) . "</td>";
                                echo "<td>" . htmlspecialchars($success['link_data']['id'] ?? 'N/A') . "</td>";
                                echo "<td style='word-break: break-all; max-width: 300px;'>" . htmlspecialchars($success['link_data']['link'] ?? 'N/A') . "</td>";
                                echo "<td style='word-break: break-all; max-width: 200px;'>" . htmlspecialchars($success['link_data']['ad_monitor_click_link'] ?? 'N/A') . "</td>";
                                echo "</tr>";
                            }
                            echo "</table>";
                        }
                        
                        // 显示失败的记录
                        if (!empty($result['data']['errors'])) {
                            echo "<h4>处理失败的记录:</h4>";
                            echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
                            echo "<tr><th>记录ID</th><th>名称</th><th>错误信息</th></tr>";
                            
                            foreach ($result['data']['errors'] as $error) {
                                echo "<tr>";
                                echo "<td>" . htmlspecialchars($error['record_id']) . "</td>";
                                echo "<td>" . htmlspecialchars($error['name']) . "</td>";
                                echo "<td style='color: red;'>" . htmlspecialchars($error['error']) . "</td>";
                                echo "</tr>";
                            }
                            echo "</table>";
                        }
                    } else {
                        echo "<div style='background-color: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px; margin: 10px 0;'>";
                        echo "<h4 style='color: #721c24; margin: 0;'>❌ API返回错误</h4>";
                        echo "<p style='margin: 10px 0 0 0;'><strong>错误代码:</strong> " . $result['code'] . "</p>";
                        echo "<p style='margin: 5px 0 0 0;'><strong>错误信息:</strong> " . htmlspecialchars($result['msg']) . "</p>";
                        echo "</div>";
                    }
                    
                    // 显示完整的响应数据
                    echo "<details style='margin: 20px 0;'>";
                    echo "<summary style='cursor: pointer; font-weight: bold;'>查看完整API响应数据</summary>";
                    echo "<pre style='background-color: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; border-radius: 5px; overflow-x: auto; font-size: 12px;'>";
                    echo htmlspecialchars(json_encode($result, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
                    echo "</pre>";
                    echo "</details>";
                } else {
                    echo "<div style='background-color: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px; margin: 10px 0;'>";
                    echo "<h4 style='color: #721c24; margin: 0;'>❌ API响应解析失败</h4>";
                    echo "<p style='margin: 10px 0 0 0;'>原始响应:</p>";
                    echo "<pre style='background-color: #f8f9fa; padding: 10px; border: 1px solid #dee2e6; border-radius: 3px; overflow-x: auto; font-size: 11px;'>";
                    echo htmlspecialchars(substr($response, 0, 1000));
                    echo "</pre>";
                    echo "</div>";
                }
            } else {
                echo "<p style='color: red;'>❌ API无响应</p>";
            }
            
            // 检查link表的变化
            echo "<h3>3. 检查link表数据变化</h3>";
            $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM link");
            $stmt->execute();
            $linkCount = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
            
            echo "<p>link 表中现在有 <strong>$linkCount</strong> 条记录</p>";
            
            if ($linkCount > 0) {
                $stmt = $pdo->prepare("SELECT id, name, app_name, appid, link, created_at FROM link ORDER BY id DESC LIMIT 5");
                $stmt->execute();
                $linkRecords = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                echo "<h4>最新的链接记录:</h4>";
                echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
                echo "<tr><th>ID</th><th>名称</th><th>应用名称</th><th>AppID</th><th>推广链接</th><th>创建时间</th></tr>";
                
                foreach ($linkRecords as $record) {
                    echo "<tr>";
                    echo "<td>" . htmlspecialchars($record['id']) . "</td>";
                    echo "<td>" . htmlspecialchars($record['name']) . "</td>";
                    echo "<td>" . htmlspecialchars($record['app_name']) . "</td>";
                    echo "<td>" . htmlspecialchars($record['appid']) . "</td>";
                    echo "<td style='word-break: break-all; max-width: 300px;'>" . htmlspecialchars($record['link']) . "</td>";
                    echo "<td>" . htmlspecialchars($record['created_at']) . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
            }
        } else {
            echo "<h2>2. 开始测试</h2>";
            echo "<form method='post'>";
            echo "<p>点击下面的按钮来测试修复后的批量创建推广链接功能：</p>";
            echo "<button type='submit' name='test_fixed_api' style='padding: 15px 30px; background-color: #007bff; color: white; border: none; cursor: pointer; font-size: 16px; border-radius: 5px;'>🚀 开始测试修复后的API</button>";
            echo "</form>";
        }
        
    } else {
        echo "<p style='color: orange;'>⚠️ create_promotion_link 表中没有数据，请先通过主页面添加一些推广链接参数。</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ 错误: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<h2>修复说明</h2>";
echo "<div style='background-color: #e7f3ff; padding: 15px; border: 1px solid #b3d9ff; border-radius: 5px;'>";
echo "<h3>主要修复内容:</h3>";
echo "<ul>";
echo "<li><strong>时间戳处理:</strong> 将timestamp参数从签名计算中分离，避免\"请求携带的时间戳不能为空\"错误</li>";
echo "<li><strong>签名逻辑:</strong> 只对业务参数进行签名，timestamp和sign不参与签名计算</li>";
echo "<li><strong>参数顺序:</strong> 确保timestamp和sign在最后添加到请求参数中</li>";
echo "<li><strong>请求间隔:</strong> 添加1秒间隔避免API频率限制</li>";
echo "</ul>";
echo "</div>";

echo "</body></html>";
?>
