<?php
// test_final_flow.php - 测试最终完整流程

require_once 'config.php';

echo "<!DOCTYPE html>";
echo "<html lang='zh-CN'>";
echo "<head><meta charset='UTF-8'><title>测试最终完整流程</title></head>";
echo "<body>";
echo "<h1>测试最终完整流程</h1>";

if (isset($_POST['test_final'])) {
    echo "<h2>开始测试最终完整流程</h2>";
    echo "<p>模拟用户提交表单 → 自动批量处理 → 生成Excel下载</p>";

    // 1. 先添加测试数据
    echo "<p>1. 添加测试数据...</p>";
    try {
        $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASSWORD);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        // 清空现有测试数据
        $pdo->exec("DELETE FROM create_promotion_link WHERE name LIKE '最终测试%'");

        // 插入新的测试数据
        $testData = [
            ['最终测试链接A', '*********', '515208', '1'],
            ['最终测试链接B', '*********', '515208', '2']
        ];

        $stmt = $pdo->prepare("INSERT INTO create_promotion_link (
            admin_account_name, account_id, project, appid,
            book_id, chapter_num, name, media_id,
            postback_rule_id, recharge_panel_id, advertiser_account_id
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");

        foreach ($testData as $data) {
            $stmt->execute([
                'aishang', // admin_account_name
                553456211129746234, // account_id
                8, // project
                'wxe3a874175a6e6ed3', // appid
                $data[2], // book_id
                $data[3], // chapter_num
                $data[0], // name
                1, // media_id
                6505, // postback_rule_id
                5501, // recharge_panel_id
                $data[1] // advertiser_account_id
            ]);
        }

        echo "<p>✅ 成功插入 " . count($testData) . " 条测试数据</p>";

        // 2. 调用批量处理
        echo "<p>2. 调用批量处理API...</p>";
        $startTime = microtime(true);

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, "http://localhost/qmxs/modules/batch_create_promotion_links.php");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 300);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        $endTime = microtime(true);
        $executionTime = round($endTime - $startTime, 2);

        echo "<p><strong>HTTP状态码:</strong> $httpCode</p>";
        echo "<p><strong>执行时间:</strong> {$executionTime}秒</p>";

        if ($response && $httpCode == 200) {
            $result = json_decode($response, true);
            if ($result && $result['code'] === 0) {
                echo "<div style='background-color: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px; margin: 10px 0;'>";
                echo "<h4 style='color: #155724; margin: 0 0 10px 0;'>✅ 批量处理成功！</h4>";
                echo "<ul style='margin: 0;'>";
                echo "<li><strong>总记录数:</strong> " . $result['data']['total_records'] . "</li>";
                echo "<li><strong>处理数量:</strong> " . $result['data']['processed'] . "</li>";
                echo "<li><strong>成功数量:</strong> " . $result['data']['success'] . "</li>";
                echo "<li><strong>失败数量:</strong> " . $result['data']['failed'] . "</li>";
                echo "</ul>";
                echo "</div>";

                // 3. 生成Excel文件
                echo "<p>3. 生成Excel文件...</p>";

                if ($result['data']['success'] > 0) {
                    // 创建下载链接
                    $excelData = base64_encode(json_encode($result['data']));
                    echo "<form method='post' action='download_excel.php' target='_blank' style='margin: 20px 0;'>";
                    echo "<input type='hidden' name='excel_data' value='$excelData'>";
                    echo "<button type='submit' style='padding: 15px 30px; background-color: #28a745; color: white; border: none; cursor: pointer; font-size: 16px; border-radius: 5px;'>📊 下载Excel文件 (.xls格式)</button>";
                    echo "</form>";

                    echo "<div style='background-color: #e7f3ff; padding: 15px; border: 1px solid #b3d9ff; border-radius: 5px; margin: 10px 0;'>";
                    echo "<h4>📋 Excel表格内容预览</h4>";
                    echo "<p><strong>表格格式:</strong> 广告主账户ID | 小程序名称(11位) | 小程序原始ID | 小程序链接(32位) | 链接名称</p>";
                    echo "<table border='1' style='border-collapse: collapse; width: 100%; font-size: 12px;'>";
                    echo "<tr style='background-color: #CCE5FF; font-weight: bold;'>";
                    echo "<th>广告主账户ID</th><th>小程序名称(11位)</th><th>小程序原始ID</th><th>小程序链接(32位)</th><th>链接名称</th>";
                    echo "</tr>";

                    foreach ($result['data']['results'] as $success) {
                        $linkData = $success['link_data'];
                        echo "<tr>";
                        echo "<td>*********</td>"; // 示例广告主账户ID
                        echo "<td>七猫小说</td>";
                        echo "<td>wxe3a874175a6e6ed3</td>"; // 小程序原始ID
                        echo "<td>" . htmlspecialchars($linkData['link'] ?? '') . "</td>";
                        echo "<td>" . htmlspecialchars($success['name']) . "</td>";
                        echo "</tr>";
                    }
                    echo "</table>";
                    echo "</div>";

                    echo "<div style='background-color: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px; margin: 20px 0;'>";
                    echo "<h4 style='color: #155724; margin: 0 0 10px 0;'>🎉 完整流程测试成功！</h4>";
                    echo "<p style='margin: 0;'>用户提交表单 → 自动批量处理 → 生成Excel下载 的完整流程已经正常工作。</p>";
                    echo "</div>";
                } else {
                    echo "<p style='color: orange;'>⚠️ 没有成功的记录，无法生成Excel文件</p>";
                }

                if (!empty($result['data']['errors'])) {
                    echo "<h4>❌ 处理失败的记录:</h4>";
                    echo "<ul>";
                    foreach ($result['data']['errors'] as $error) {
                        echo "<li style='color: red;'>" . htmlspecialchars($error['name']) . ": " . htmlspecialchars($error['error']) . "</li>";
                    }
                    echo "</ul>";
                }

            } else {
                echo "<div style='background-color: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px; margin: 10px 0;'>";
                echo "<h4 style='color: #721c24; margin: 0;'>❌ 批量处理失败</h4>";
                echo "<p style='margin: 10px 0 0 0;'><strong>错误:</strong> " . htmlspecialchars($result['msg'] ?? '未知错误') . "</p>";
                echo "</div>";
            }
        } else {
            echo "<p style='color: red;'>❌ API调用失败，HTTP状态码: $httpCode</p>";
            if ($response) {
                echo "<pre style='background-color: #f8f9fa; padding: 10px; border: 1px solid #dee2e6; border-radius: 3px; overflow-x: auto; font-size: 11px;'>";
                echo htmlspecialchars(substr($response, 0, 1000));
                echo "</pre>";
            }
        }

    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ 错误: " . htmlspecialchars($e->getMessage()) . "</p>";
    }

} else {
    echo "<div style='background-color: #e7f3ff; padding: 15px; border: 1px solid #b3d9ff; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>🎯 最终完整流程测试</h3>";
    echo "<p>此测试将验证从用户提交到Excel下载的完整自动化流程：</p>";
    echo "<ol>";
    echo "<li><strong>数据插入:</strong> 模拟用户提交表单，插入测试数据</li>";
    echo "<li><strong>自动处理:</strong> 自动调用批量处理API，1.5秒间隔</li>";
    echo "<li><strong>Excel生成:</strong> 根据处理结果生成标准格式Excel文件</li>";
    echo "<li><strong>文件下载:</strong> 提供.xls格式文件下载</li>";
    echo "</ol>";
    echo "<p><strong>Excel表格格式:</strong></p>";
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; font-size: 12px;'>";
    echo "<tr style='background-color: #CCE5FF; font-weight: bold;'>";
    echo "<th>广告主账户ID</th><th>小程序名称(11位)</th><th>小程序原始ID</th><th>小程序链接(32位)</th><th>链接名称</th>";
    echo "</tr>";
    echo "<tr>";
    echo "<td>*********</td><td>七猫小说</td><td>wxe3a874175a6e6ed3</td><td>pages/index/index?id=515208&cnum=1&channel=...</td><td>最终测试链接A</td>";
    echo "</tr>";
    echo "</table>";
    echo "<form method='post'>";
    echo "<button type='submit' name='test_final' style='padding: 15px 30px; background-color: #007bff; color: white; border: none; cursor: pointer; font-size: 16px; border-radius: 5px;'>🚀 开始最终完整流程测试</button>";
    echo "</form>";
    echo "</div>";
}

echo "</body></html>";
?>
