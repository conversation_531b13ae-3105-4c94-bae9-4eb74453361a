<?php
// batch_create_links.php - 批量调度器，按1.5秒间隔多次调用create_promotion_link.php

// 设置内部编码
mb_internal_encoding('UTF-8');
header('Content-Type: application/json; charset=UTF-8');

require_once 'config.php';

try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASSWORD);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // 检查待处理的记录数量
    $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM create_promotion_link");
    $stmt->execute();
    $totalRecords = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    if ($totalRecords == 0) {
        echo json_encode([
            'code' => -3,
            'msg' => '没有待处理的推广链接记录',
            'data' => [
                'total_records' => 0,
                'processed' => 0,
                'success' => 0,
                'failed' => 0
            ],
            'request_id' => uniqid('batch_', true)
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    
    error_log("批量调度器开始处理，共 $totalRecords 条记录");
    
    $processedCount = 0;
    $successCount = 0;
    $failedCount = 0;
    $results = [];
    $errors = [];
    $startTime = microtime(true);
    
    // 循环处理，直到没有记录为止
    while (true) {
        // 检查是否还有待处理的记录
        $stmt = $pdo->prepare("SELECT COUNT(*) as remaining FROM create_promotion_link");
        $stmt->execute();
        $remaining = $stmt->fetch(PDO::FETCH_ASSOC)['remaining'];
        
        if ($remaining == 0) {
            error_log("所有记录处理完成");
            break;
        }
        
        error_log("剩余 $remaining 条记录待处理");
        
        // 调用 create_promotion_link.php 处理一条记录
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, "http://localhost/qmxs/modules/create_promotion_link.php");
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 60);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curlError = curl_error($ch);
        curl_close($ch);
        
        $processedCount++;
        
        if ($curlError) {
            error_log("CURL错误: $curlError");
            $errors[] = [
                'sequence' => $processedCount,
                'error' => "CURL错误: $curlError",
                'http_code' => $httpCode
            ];
            $failedCount++;
        } else if ($response) {
            $result = json_decode($response, true);
            if ($result && isset($result['code'])) {
                if ($result['code'] === 0) {
                    // 成功
                    $successCount++;
                    $results[] = [
                        'sequence' => $processedCount,
                        'record_id' => $result['data']['record_id'] ?? null,
                        'name' => $result['data']['name'] ?? null,
                        'status' => 'success',
                        'link_data' => $result['data']['link_data'] ?? null
                    ];
                    error_log("成功处理记录 " . ($result['data']['record_id'] ?? 'unknown'));
                } else {
                    // API返回错误
                    $failedCount++;
                    $errors[] = [
                        'sequence' => $processedCount,
                        'record_id' => $result['data']['record_id'] ?? null,
                        'name' => $result['data']['name'] ?? null,
                        'error' => $result['msg'] ?? '未知错误',
                        'status' => 'api_error'
                    ];
                    error_log("API错误: " . ($result['msg'] ?? '未知错误'));
                }
            } else {
                // 响应解析失败
                $failedCount++;
                $errors[] = [
                    'sequence' => $processedCount,
                    'error' => '响应解析失败',
                    'raw_response' => substr($response, 0, 200),
                    'status' => 'parse_error'
                ];
                error_log("响应解析失败: " . substr($response, 0, 100));
            }
        } else {
            // 无响应
            $failedCount++;
            $errors[] = [
                'sequence' => $processedCount,
                'error' => 'API无响应',
                'http_code' => $httpCode,
                'status' => 'no_response'
            ];
            error_log("API无响应，HTTP状态码: $httpCode");
        }
        
        // 检查是否还有更多记录需要处理
        $stmt = $pdo->prepare("SELECT COUNT(*) as remaining FROM create_promotion_link");
        $stmt->execute();
        $stillRemaining = $stmt->fetch(PDO::FETCH_ASSOC)['remaining'];
        
        // 如果还有记录，等待1.5秒再处理下一条
        if ($stillRemaining > 0) {
            error_log("等待1.5秒后处理下一条记录...");
            usleep(1500000); // 1.5秒间隔
        }
        
        // 安全检查：避免无限循环
        if ($processedCount >= $totalRecords + 10) {
            error_log("安全检查：处理次数超过预期，停止处理");
            break;
        }
    }
    
    $endTime = microtime(true);
    $totalTime = round($endTime - $startTime, 2);
    
    // 构建最终返回数据
    $return_data = [
        'code' => 0,
        'msg' => 'ok',
        'data' => [
            'total_records' => $totalRecords,
            'processed' => $processedCount,
            'success' => $successCount,
            'failed' => $failedCount,
            'execution_time' => $totalTime,
            'average_time_per_record' => $processedCount > 0 ? round($totalTime / $processedCount, 2) : 0,
            'results' => $results,
            'errors' => $errors
        ],
        'request_id' => uniqid('batch_', true)
    ];
    
    error_log("批量处理完成: 总数=$totalRecords, 处理=$processedCount, 成功=$successCount, 失败=$failedCount, 耗时={$totalTime}秒");
    
    echo json_encode($return_data, JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    error_log("批量调度器错误: " . $e->getMessage());
    echo json_encode([
        'code' => -1,
        'msg' => '批量调度器错误: ' . $e->getMessage(),
        'data' => null,
        'request_id' => uniqid('batch_', true)
    ], JSON_UNESCAPED_UNICODE);
}
?>
