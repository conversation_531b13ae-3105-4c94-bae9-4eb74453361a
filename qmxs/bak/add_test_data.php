<?php
// add_test_data.php - 添加测试数据

require_once 'config.php';

try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASSWORD);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    echo "添加测试数据到 create_promotion_link 表...\n";

    // 清空现有测试数据
    $pdo->exec("DELETE FROM create_promotion_link WHERE name LIKE '测试链接%'");
    echo "清空现有测试数据\n";

    // 插入新的测试数据（使用可投放的章节1、2、3）
    $testData = [
        ['提交流程测试A', '*********', '515208', '1'],
        ['提交流程测试B', '*********', '515208', '2']
    ];

    $stmt = $pdo->prepare("INSERT INTO create_promotion_link (
        admin_account_name, account_id, project, appid,
        book_id, chapter_num, name, media_id,
        postback_rule_id, recharge_panel_id, advertiser_account_id
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");

    foreach ($testData as $data) {
        $stmt->execute([
            'aishang', // admin_account_name
            553456211129746234, // account_id
            8, // project
            'wxe3a874175a6e6ed3', // appid
            $data[2], // book_id
            $data[3], // chapter_num
            $data[0], // name
            1, // media_id
            6505, // postback_rule_id
            5501, // recharge_panel_id
            $data[1] // advertiser_account_id
        ]);
        echo "插入数据: " . $data[0] . "\n";
    }

    echo "成功插入 " . count($testData) . " 条测试数据\n";

    // 检查数据
    $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM create_promotion_link");
    $stmt->execute();
    $count = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    echo "当前 create_promotion_link 表中共有 $count 条记录\n";

} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}
?>
