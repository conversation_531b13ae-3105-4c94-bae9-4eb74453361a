<?php
// test_chapter_default.php - 测试章节序号默认值功能

// 设置内部编码和页面编码
mb_internal_encoding('UTF-8');
header('Content-Type: text/html; charset=UTF-8');

echo "<!DOCTYPE html>";
echo "<html lang='zh-CN'>";
echo "<head><meta charset='UTF-8'><title>章节序号默认值测试</title></head>";
echo "<body>";
echo "<h1>投放章节序号ID默认值测试</h1>";

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $bookNames = $_POST['book_names'] ?? '';
    $bookIds = $_POST['book_ids'] ?? '';
    $chapterNumbers = $_POST['chapter_numbers'] ?? '';

    echo "<h2>输入数据</h2>";
    echo "链接名称: <pre>" . htmlspecialchars($bookNames) . "</pre>";
    echo "投放书籍ID: <pre>" . htmlspecialchars($bookIds) . "</pre>";
    echo "投放章节序号: <pre>" . htmlspecialchars($chapterNumbers) . "</pre>";

    echo "<h2>处理逻辑测试</h2>";

    // 模拟主页面的处理逻辑
    $bookNames = mb_convert_encoding($bookNames, 'UTF-8', mb_detect_encoding($bookNames, 'UTF-8, GBK, GB2312, BIG5', true));

    // 先处理 book_names 以确定行数
    $names = array_filter(array_map(function($item) {
        $trimmed = trim($item);
        return mb_convert_encoding($trimmed, 'UTF-8', mb_detect_encoding($trimmed, 'UTF-8, GBK, GB2312, BIG5', true));
    }, explode("\n", $bookNames)));

    $validNames = array_filter($names, function($name) {
        return !empty($name) && mb_check_encoding($name, 'UTF-8');
    });

    if (empty($validNames)) {
        echo "<p style='color:red;'>❌ 链接名称不能为空或包含无效字符！</p>";
    } else {
        $nameCount = count($validNames);
        echo "<p>✅ 有效链接名称数量: <strong>$nameCount</strong></p>";
        echo "<p>有效名称列表:</p><ul>";
        foreach ($validNames as $i => $name) {
            echo "<li>[$i] " . htmlspecialchars($name) . "</li>";
        }
        echo "</ul>";

        // 测试默认值生成逻辑
        echo "<h3>默认值生成测试</h3>";

        $originalBookIds = $bookIds;
        $originalChapterNumbers = $chapterNumbers;

        // 修复：根据链接名称的行数来设置默认值（完整逻辑）
        if (empty($bookIds)) {
            $bookIds = str_repeat("1\n", $nameCount - 1) . "1"; // 生成对应行数的 "1"
            echo "<p>✅ 自动生成 book_ids (原为空): <pre>" . htmlspecialchars($bookIds) . "</pre></p>";
        } else {
            echo "<p>📝 book_ids 用户输入: <pre>" . htmlspecialchars($originalBookIds) . "</pre></p>";
            // 检查用户输入的行数是否足够，不够则补充默认值
            $userBookIds = array_map('trim', explode("\n", $bookIds));
            $userBookIdsCount = count(array_filter($userBookIds, function($id) { return !empty($id); }));
            if ($userBookIdsCount < $nameCount) {
                $needMore = $nameCount - $userBookIdsCount;
                $bookIds .= "\n" . str_repeat("1\n", $needMore - 1) . "1";
                echo "<p>🔧 补充 book_ids: 用户输入 $userBookIdsCount 行，需要 $nameCount 行，补充了 $needMore 行</p>";
                echo "<p>补充后的 book_ids: <pre>" . htmlspecialchars($bookIds) . "</pre></p>";
            }
        }

        if (empty($chapterNumbers)) {
            $chapterNumbers = str_repeat("1\n", $nameCount - 1) . "1"; // 生成对应行数的 "1"
            echo "<p>✅ 自动生成 chapter_numbers (原为空): <pre>" . htmlspecialchars($chapterNumbers) . "</pre></p>";
        } else {
            echo "<p>📝 chapter_numbers 用户输入: <pre>" . htmlspecialchars($originalChapterNumbers) . "</pre></p>";
            // 检查用户输入的行数是否足够，不够则补充默认值
            $userChapterNumbers = array_map('trim', explode("\n", $chapterNumbers));
            $userChapterNumbersCount = count(array_filter($userChapterNumbers, function($ch) { return !empty($ch); }));
            if ($userChapterNumbersCount < $nameCount) {
                $needMore = $nameCount - $userChapterNumbersCount;
                $chapterNumbers .= "\n" . str_repeat("1\n", $needMore - 1) . "1";
                echo "<p>🔧 补充 chapter_numbers: 用户输入 $userChapterNumbersCount 行，需要 $nameCount 行，补充了 $needMore 行</p>";
                echo "<p>补充后的 chapter_numbers: <pre>" . htmlspecialchars($chapterNumbers) . "</pre></p>";
            }
        }

        // 转换为数组并验证
        echo "<h3>数组转换验证</h3>";
        $ids = array_map('trim', explode("\n", $bookIds));
        $chapters = array_map('trim', explode("\n", $chapterNumbers));

        echo "<p>book_ids 数组: ";
        var_dump($ids);
        echo "</p>";

        echo "<p>chapter_numbers 数组: ";
        var_dump($chapters);
        echo "</p>";

        // 验证数据匹配
        echo "<h3>数据匹配验证</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>索引</th><th>链接名称</th><th>书籍ID</th><th>章节序号</th><th>状态</th></tr>";

        foreach ($validNames as $index => $name) {
            $bookId = $ids[$index] ?? '未设置';
            $chapterNum = $chapters[$index] ?? '未设置';
            $status = ($bookId !== '未设置' && $chapterNum !== '未设置') ? '✅ 完整' : '❌ 缺失';

            echo "<tr>";
            echo "<td>$index</td>";
            echo "<td>" . htmlspecialchars($name) . "</td>";
            echo "<td>$bookId</td>";
            echo "<td>$chapterNum</td>";
            echo "<td>$status</td>";
            echo "</tr>";
        }
        echo "</table>";

        // 测试不同情况
        echo "<h3>测试结果总结</h3>";
        $bookIdsCount = count(array_filter($ids, function($id) { return !empty($id); }));
        $chaptersCount = count(array_filter($chapters, function($ch) { return !empty($ch); }));

        echo "<ul>";
        echo "<li>链接名称数量: $nameCount</li>";
        echo "<li>有效书籍ID数量: $bookIdsCount</li>";
        echo "<li>有效章节序号数量: $chaptersCount</li>";
        echo "<li>数据完整性: " . ($nameCount == $bookIdsCount && $nameCount == $chaptersCount ? '✅ 完整匹配' : '❌ 数量不匹配') . "</li>";
        echo "</ul>";
    }
}

echo "<h2>测试表单</h2>";
echo "<form method='post' accept-charset='UTF-8'>";

echo "<div style='margin-bottom: 15px;'>";
echo "<label>链接名称（一行一个名称）：</label><br>";
echo "<textarea name='book_names' rows='5' cols='50' placeholder='请输入名称，每行一个名称' style='width: 100%;'>" . htmlspecialchars($_POST['book_names'] ?? '') . "</textarea>";
echo "</div>";

echo "<div style='margin-bottom: 15px;'>";
echo "<label>投放书籍ID（一行一个ID，留空测试默认值）：</label><br>";
echo "<textarea name='book_ids' rows='5' cols='50' placeholder='留空将自动生成默认值1' style='width: 100%;'>" . htmlspecialchars($_POST['book_ids'] ?? '') . "</textarea>";
echo "</div>";

echo "<div style='margin-bottom: 15px;'>";
echo "<label>投放章节序号ID（一行一个ID，留空测试默认值）：</label><br>";
echo "<textarea name='chapter_numbers' rows='5' cols='50' placeholder='留空将自动生成默认值1' style='width: 100%;'>" . htmlspecialchars($_POST['chapter_numbers'] ?? '') . "</textarea>";
echo "</div>";

echo "<button type='submit' style='padding: 10px 20px; background-color: #4CAF50; color: white; border: none; cursor: pointer;'>开始测试</button>";
echo "</form>";

echo "<h2>测试说明</h2>";
echo "<ul>";
echo "<li><strong>测试场景1</strong>: 只填写链接名称，其他留空 → 应该自动生成对应行数的 '1'</li>";
echo "<li><strong>测试场景2</strong>: 填写链接名称和部分书籍ID，章节序号留空 → 章节序号应该自动生成</li>";
echo "<li><strong>测试场景3</strong>: 链接名称3行，书籍ID只填1行 → 应该自动补充2行 '1'</li>";
echo "<li><strong>测试场景4</strong>: 链接名称5行，章节序号只填2行 → 应该自动补充3行 '1'</li>";
echo "<li><strong>测试场景5</strong>: 全部填写且行数匹配 → 使用用户输入的值</li>";
echo "<li><strong>预期结果</strong>: 每个链接名称都应该有对应的书籍ID和章节序号</li>";
echo "</ul>";

echo "<h2>示例测试数据</h2>";
echo "<div style='background-color: #f5f5f5; padding: 10px; margin: 10px 0;'>";
echo "<h3>场景1: 完全自动生成</h3>";
echo "<strong>链接名称:</strong><br>测试名称1<br>测试名称2<br>测试名称3<br>";
echo "<strong>书籍ID:</strong> (留空)<br>";
echo "<strong>章节序号:</strong> (留空)<br>";
echo "<strong>预期结果:</strong> 自动生成3行 '1'<br>";
echo "</div>";

echo "<div style='background-color: #f5f5f5; padding: 10px; margin: 10px 0;'>";
echo "<h3>场景2: 部分自动补充</h3>";
echo "<strong>链接名称:</strong><br>测试名称1<br>测试名称2<br>测试名称3<br>";
echo "<strong>书籍ID:</strong><br>100<br>";
echo "<strong>章节序号:</strong><br>5<br>6<br>";
echo "<strong>预期结果:</strong> 书籍ID补充2行 '1'，章节序号补充1行 '1'<br>";
echo "</div>";

echo "</body></html>";
?>
