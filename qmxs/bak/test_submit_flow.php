<?php
// test_submit_flow.php - 测试提交流程

require_once 'config.php';

echo "<!DOCTYPE html>";
echo "<html lang='zh-CN'>";
echo "<head><meta charset='UTF-8'><title>测试提交流程</title></head>";
echo "<body>";
echo "<h1>测试提交流程</h1>";

if (isset($_POST['test_submit'])) {
    echo "<h2>模拟表单提交</h2>";
    
    // 模拟POST数据到index.php
    $postData = [
        'recharge_account_name' => 'aishang',
        'recharge_panel_name' => '七猫小说充值模板',
        'postback_rule_name' => '七猫小说回传规则',
        'project' => '8',
        'app_name' => '七猫小说',
        'media_id' => '1',
        'book_names' => "提交测试链接A\n提交测试链接B",
        'advertiser_account_ids' => "*********\n888777667",
        'book_ids' => "515208\n515208",
        'chapter_numbers' => "1\n2"
    ];
    
    echo "<p>提交的数据:</p>";
    echo "<pre style='background-color: #f8f9fa; padding: 10px; border: 1px solid #dee2e6; border-radius: 3px;'>";
    foreach ($postData as $key => $value) {
        echo htmlspecialchars($key) . ": " . htmlspecialchars($value) . "\n";
    }
    echo "</pre>";
    
    // 发送POST请求到index.php
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
    $baseUrl = $protocol . '://' . $host . dirname($_SERVER['REQUEST_URI']);
    $indexUrl = $baseUrl . '/index.php';
    
    echo "<p><strong>提交到:</strong> $indexUrl</p>";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $indexUrl);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($postData));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 600); // 10分钟超时
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/x-www-form-urlencoded'
    ]);
    
    $startTime = microtime(true);
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
    $curlError = curl_error($ch);
    curl_close($ch);
    $endTime = microtime(true);
    
    $executionTime = round($endTime - $startTime, 2);
    
    echo "<h3>提交结果</h3>";
    echo "<p><strong>HTTP状态码:</strong> $httpCode</p>";
    echo "<p><strong>内容类型:</strong> $contentType</p>";
    echo "<p><strong>执行时间:</strong> {$executionTime}秒</p>";
    
    if ($curlError) {
        echo "<p style='color: red;'><strong>CURL错误:</strong> $curlError</p>";
    }
    
    if ($httpCode == 200) {
        // 检查是否是Excel文件
        if (strpos($contentType, 'excel') !== false || strpos($contentType, 'spreadsheet') !== false) {
            echo "<div style='background-color: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px; margin: 10px 0;'>";
            echo "<h4 style='color: #155724; margin: 0 0 10px 0;'>✅ 成功！Excel文件已生成</h4>";
            echo "<p>响应是Excel文件，大小: " . strlen($response) . " 字节</p>";
            
            // 保存Excel文件供下载
            $filename = 'submit_test_result_' . date('Y-m-d_H-i-s') . '.xls';
            file_put_contents($filename, $response);
            
            echo "<p><a href='$filename' download style='padding: 10px 20px; background-color: #28a745; color: white; text-decoration: none; border-radius: 5px;'>📊 下载Excel文件</a></p>";
            echo "</div>";
            
        } else if (strpos($response, '<script>alert(') !== false) {
            // 检查是否是错误提示
            echo "<div style='background-color: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px; margin: 10px 0;'>";
            echo "<h4 style='color: #721c24; margin: 0 0 10px 0;'>❌ 处理失败</h4>";
            echo "<p>返回了错误提示页面</p>";
            echo "</div>";
            
            // 提取alert中的错误信息
            if (preg_match("/alert\('([^']+)'\)/", $response, $matches)) {
                echo "<p><strong>错误信息:</strong> " . htmlspecialchars($matches[1]) . "</p>";
            }
            
        } else {
            echo "<div style='background-color: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; border-radius: 5px; margin: 10px 0;'>";
            echo "<h4 style='color: #856404; margin: 0 0 10px 0;'>⚠️ 响应不是Excel文件</h4>";
            echo "<p>可能是HTML页面或其他内容</p>";
            echo "</div>";
        }
        
        // 显示响应内容（前1000字符）
        echo "<h4>响应内容预览:</h4>";
        echo "<pre style='background-color: #f8f9fa; padding: 10px; border: 1px solid #dee2e6; border-radius: 3px; overflow-x: auto; font-size: 11px; max-height: 300px;'>";
        echo htmlspecialchars(substr($response, 0, 1000));
        if (strlen($response) > 1000) {
            echo "\n... (响应内容被截断，总长度: " . strlen($response) . " 字符)";
        }
        echo "</pre>";
        
    } else {
        echo "<div style='background-color: #f8d7da; padding: 15px; border: 1px solid #f5c6cb; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4 style='color: #721c24; margin: 0;'>❌ 请求失败</h4>";
        echo "<p style='margin: 10px 0 0 0;'>HTTP状态码: $httpCode</p>";
        echo "</div>";
        
        if ($response) {
            echo "<h4>错误响应:</h4>";
            echo "<pre style='background-color: #f8f9fa; padding: 10px; border: 1px solid #dee2e6; border-radius: 3px; overflow-x: auto; font-size: 11px;'>";
            echo htmlspecialchars(substr($response, 0, 1000));
            echo "</pre>";
        }
    }
    
    // 检查数据库状态
    echo "<h3>数据库状态检查</h3>";
    try {
        $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASSWORD);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // 检查create_promotion_link表
        $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM create_promotion_link WHERE name LIKE '提交测试%'");
        $stmt->execute();
        $createCount = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
        echo "<p><strong>create_promotion_link表中的测试记录:</strong> $createCount 条</p>";
        
        // 检查link表
        $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM link WHERE name LIKE '提交测试%'");
        $stmt->execute();
        $linkCount = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
        echo "<p><strong>link表中的测试记录:</strong> $linkCount 条</p>";
        
        if ($createCount > 0 && $linkCount == 0) {
            echo "<p style='color: orange;'>⚠️ 数据已插入create_promotion_link表，但没有生成推广链接到link表</p>";
        } else if ($linkCount > 0) {
            echo "<p style='color: green;'>✅ 推广链接已成功生成到link表</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ 数据库检查失败: " . htmlspecialchars($e->getMessage()) . "</p>";
    }
    
} else {
    echo "<div style='background-color: #e7f3ff; padding: 15px; border: 1px solid #b3d9ff; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>🧪 提交流程测试</h3>";
    echo "<p>此测试将模拟用户在主页面提交表单的完整流程：</p>";
    echo "<ol>";
    echo "<li><strong>提交表单数据</strong> 到 index.php</li>";
    echo "<li><strong>数据插入</strong> 到 create_promotion_link 表</li>";
    echo "<li><strong>自动批量处理</strong> 调用API创建推广链接</li>";
    echo "<li><strong>Excel生成</strong> 并自动下载</li>";
    echo "</ol>";
    echo "<p><strong>测试数据:</strong></p>";
    echo "<ul>";
    echo "<li>链接名称: 提交测试链接A, 提交测试链接B</li>";
    echo "<li>广告主账户ID: *********, 888777667</li>";
    echo "<li>书籍ID: 515208 (两条)</li>";
    echo "<li>章节序号: 1, 2</li>";
    echo "</ul>";
    echo "<form method='post'>";
    echo "<button type='submit' name='test_submit' style='padding: 15px 30px; background-color: #007bff; color: white; border: none; cursor: pointer; font-size: 16px; border-radius: 5px;'>🚀 开始提交流程测试</button>";
    echo "</form>";
    echo "</div>";
}

echo "</body></html>";
?>
