<?php
// clean_test_data.php - 清理测试数据

require_once 'config.php';

try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASSWORD);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "清理测试数据...\n";
    
    // 清理create_promotion_link表中的测试数据
    $stmt = $pdo->prepare("DELETE FROM create_promotion_link WHERE name LIKE '%测试%' OR name LIKE '%最终%' OR name LIKE '%提交%'");
    $stmt->execute();
    $deleted1 = $stmt->rowCount();
    echo "从 create_promotion_link 表删除了 $deleted1 条测试记录\n";
    
    // 清理link表中的测试数据
    $stmt = $pdo->prepare("DELETE FROM link WHERE name LIKE '%测试%' OR name LIKE '%最终%' OR name LIKE '%提交%'");
    $stmt->execute();
    $deleted2 = $stmt->rowCount();
    echo "从 link 表删除了 $deleted2 条测试记录\n";
    
    // 检查剩余记录
    $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM create_promotion_link");
    $stmt->execute();
    $count1 = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    $stmt = $pdo->prepare("SELECT COUNT(*) as total FROM link");
    $stmt->execute();
    $count2 = $stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    echo "清理完成:\n";
    echo "- create_promotion_link 表剩余: $count1 条记录\n";
    echo "- link 表剩余: $count2 条记录\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}
?>
