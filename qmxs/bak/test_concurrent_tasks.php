<?php
require_once 'config.php';
require_once 'classes/TaskQueue.php';

echo "<!DOCTYPE html>";
echo "<html><head><meta charset='UTF-8'><title>测试并发任务处理</title>";
echo "<style>body{font-family:Arial,sans-serif;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;} table{border-collapse:collapse;width:100%;} th,td{border:1px solid #ddd;padding:8px;text-align:left;}</style>";
echo "</head><body>";
echo "<h1>测试并发任务处理</h1>";

try {
    // 连接数据库
    $conn = new mysqli(DB_HOST, DB_USER, DB_PASSWORD, DB_NAME);
    if ($conn->connect_error) {
        throw new Exception("连接失败: " . $conn->connect_error);
    }
    $conn->set_charset("utf8mb4");

    // 创建TaskQueue实例
    $taskQueue = new TaskQueue($conn);

    echo "<h2>步骤1: 清理旧的测试数据</h2>";
    
    // 清理旧的测试数据
    $conn->query("DELETE FROM create_promotion_link WHERE name LIKE '并发测试%'");
    $conn->query("DELETE FROM link WHERE name LIKE '并发测试%'");
    echo "<p class='success'>✅ 已清理旧的测试数据</p>";

    echo "<h2>步骤2: 创建两个测试任务</h2>";

    // 创建第一个任务
    $taskData1 = [
        [
            'admin_account_name' => 'test_user',
            'account_id' => **********,
            'project' => 1,
            'appid' => 'wxe3a874175a6e6ed3',
            'book_id' => 1001,
            'chapter_num' => 1,
            'name' => '并发测试1-记录1',
            'media_id' => 1,
            'postback_rule_id' => 1,
            'recharge_panel_id' => 1,
            'advertiser_account_id' => **********
        ],
        [
            'admin_account_name' => 'test_user',
            'account_id' => **********,
            'project' => 1,
            'appid' => 'wxe3a874175a6e6ed3',
            'book_id' => 1002,
            'chapter_num' => 1,
            'name' => '并发测试1-记录2',
            'media_id' => 1,
            'postback_rule_id' => 1,
            'recharge_panel_id' => 1,
            'advertiser_account_id' => **********
        ]
    ];

    $taskId1 = $taskQueue->createTask($taskData1);
    echo "<p class='success'>✅ 任务1创建成功: $taskId1</p>";

    // 创建第二个任务
    $taskData2 = [
        [
            'admin_account_name' => 'test_user',
            'account_id' => **********,
            'project' => 1,
            'appid' => 'wxe3a874175a6e6ed3',
            'book_id' => 2001,
            'chapter_num' => 1,
            'name' => '并发测试2-记录1',
            'media_id' => 1,
            'postback_rule_id' => 1,
            'recharge_panel_id' => 1,
            'advertiser_account_id' => **********
        ],
        [
            'admin_account_name' => 'test_user',
            'account_id' => **********,
            'project' => 1,
            'appid' => 'wxe3a874175a6e6ed3',
            'book_id' => 2002,
            'chapter_num' => 1,
            'name' => '并发测试2-记录2',
            'media_id' => 1,
            'postback_rule_id' => 1,
            'recharge_panel_id' => 1,
            'advertiser_account_id' => **********
        ]
    ];

    $taskId2 = $taskQueue->createTask($taskData2);
    echo "<p class='success'>✅ 任务2创建成功: $taskId2</p>";

    echo "<h2>步骤3: 模拟并发处理</h2>";

    // 获取基础URL
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
    $baseUrl = $protocol . '://' . $host . dirname($_SERVER['REQUEST_URI']);
    $processorUrl = $baseUrl . '/modules/task_queue_processor.php';

    echo "<p class='info'>任务处理器URL: $processorUrl</p>";

    // 启动第一个任务处理器（模拟第一个用户的请求）
    echo "<h3>启动第一个任务处理器</h3>";
    $ch1 = curl_init();
    curl_setopt($ch1, CURLOPT_URL, $processorUrl);
    curl_setopt($ch1, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch1, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch1, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch1, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch1, CURLOPT_SSL_VERIFYHOST, false);
    
    $response1 = curl_exec($ch1);
    $httpCode1 = curl_getinfo($ch1, CURLINFO_HTTP_CODE);
    curl_close($ch1);

    echo "<p>HTTP状态码: $httpCode1</p>";
    echo "<h4>第一个处理器响应:</h4>";
    echo "<pre>" . htmlspecialchars($response1) . "</pre>";

    $result1 = json_decode($response1, true);
    if ($result1) {
        if ($result1['code'] === 0) {
            echo "<p class='success'>✅ 第一个任务处理成功</p>";
        } else {
            echo "<p class='error'>❌ 第一个任务处理失败: " . ($result1['msg'] ?? '未知错误') . "</p>";
        }
    }

    // 等待一小段时间，然后启动第二个任务处理器（模拟第二个用户的请求）
    echo "<h3>立即启动第二个任务处理器（模拟并发冲突）</h3>";
    $ch2 = curl_init();
    curl_setopt($ch2, CURLOPT_URL, $processorUrl);
    curl_setopt($ch2, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch2, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch2, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch2, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch2, CURLOPT_SSL_VERIFYHOST, false);
    
    $response2 = curl_exec($ch2);
    $httpCode2 = curl_getinfo($ch2, CURLINFO_HTTP_CODE);
    curl_close($ch2);

    echo "<p>HTTP状态码: $httpCode2</p>";
    echo "<h4>第二个处理器响应:</h4>";
    echo "<pre>" . htmlspecialchars($response2) . "</pre>";

    $result2 = json_decode($response2, true);
    if ($result2) {
        if ($result2['code'] === 0) {
            echo "<p class='success'>✅ 第二个任务处理成功</p>";
        } else {
            echo "<p class='info'>ℹ️ 第二个任务处理结果: " . ($result2['msg'] ?? '未知错误') . "</p>";
            if (isset($result2['data']['status']) && $result2['data']['status'] === 'failed') {
                echo "<p class='success'>✅ 并发冲突检测正常工作，第二个任务已正确标记为失败</p>";
            }
        }
    }

    echo "<h2>步骤4: 检查任务状态</h2>";

    // 检查任务1状态
    $status1 = $taskQueue->getTaskStatus($taskId1);
    echo "<h3>任务1状态 ($taskId1):</h3>";
    if ($status1) {
        echo "<table>";
        echo "<tr><th>总记录数</th><th>完成数</th><th>失败数</th><th>创建时间</th><th>更新时间</th></tr>";
        echo "<tr>";
        echo "<td>" . $status1['total_count'] . "</td>";
        echo "<td>" . $status1['done_count'] . "</td>";
        echo "<td>" . $status1['failed_count'] . "</td>";
        echo "<td>" . $status1['created_at'] . "</td>";
        echo "<td>" . $status1['updated_at'] . "</td>";
        echo "</tr>";
        echo "</table>";
    }

    // 检查任务2状态
    $status2 = $taskQueue->getTaskStatus($taskId2);
    echo "<h3>任务2状态 ($taskId2):</h3>";
    if ($status2) {
        echo "<table>";
        echo "<tr><th>总记录数</th><th>完成数</th><th>失败数</th><th>创建时间</th><th>更新时间</th></tr>";
        echo "<tr>";
        echo "<td>" . $status2['total_count'] . "</td>";
        echo "<td>" . $status2['done_count'] . "</td>";
        echo "<td>" . $status2['failed_count'] . "</td>";
        echo "<td>" . $status2['created_at'] . "</td>";
        echo "<td>" . $status2['updated_at'] . "</td>";
        echo "</tr>";
        echo "</table>";
    }

    // 显示详细的任务记录
    echo "<h2>步骤5: 详细任务记录</h2>";
    
    $stmt = $conn->prepare("SELECT task_id, name, status, error_message, created_at, updated_at FROM create_promotion_link WHERE name LIKE '并发测试%' ORDER BY task_id, created_at");
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        echo "<table>";
        echo "<tr><th>任务ID</th><th>名称</th><th>状态</th><th>错误信息</th><th>创建时间</th><th>更新时间</th></tr>";
        
        while ($row = $result->fetch_assoc()) {
            $statusColor = '';
            switch($row['status']) {
                case 'done': $statusColor = 'color:green;'; break;
                case 'failed': $statusColor = 'color:red;'; break;
                case 'running': $statusColor = 'color:blue;'; break;
                case 'pending': $statusColor = 'color:orange;'; break;
            }
            
            echo "<tr>";
            echo "<td>" . htmlspecialchars($row['task_id']) . "</td>";
            echo "<td>" . htmlspecialchars($row['name']) . "</td>";
            echo "<td style='$statusColor'>" . $row['status'] . "</td>";
            echo "<td>" . htmlspecialchars($row['error_message'] ?? '') . "</td>";
            echo "<td>" . $row['created_at'] . "</td>";
            echo "<td>" . $row['updated_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }

    echo "<h2>测试完成</h2>";
    echo "<p class='info'>现在可以尝试手动处理失败的任务2：</p>";
    echo "<p><a href='task_status.php?task_id=$taskId2' target='_blank'>查看任务2状态并手动处理</a></p>";

} catch (Exception $e) {
    echo "<p class='error'>❌ 错误: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "</body></html>";
?>
