<?php
require_once 'config.php';
require_once 'classes/TaskQueue.php';

echo "<!DOCTYPE html>";
echo "<html><head><meta charset='UTF-8'><title>完整修复测试</title>";
echo "<style>body{font-family:Arial,sans-serif;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;} table{border-collapse:collapse;width:100%;} th,td{border:1px solid #ddd;padding:8px;text-align:left;} th{background:#f2f2f2;}</style>";
echo "</head><body>";
echo "<h1>完整修复测试</h1>";

try {
    // 连接数据库
    $conn = new mysqli(DB_HOST, DB_USER, DB_PASSWORD, DB_NAME);
    if ($conn->connect_error) {
        throw new Exception("连接失败: " . $conn->connect_error);
    }
    $conn->set_charset("utf8mb4");

    // 清理之前的测试数据
    $conn->query("DELETE FROM create_promotion_link WHERE name LIKE '完整修复测试%'");
    $conn->query("DELETE FROM link WHERE name LIKE '完整修复测试%'");
    echo "<p class='success'>✅ 清理旧测试数据完成</p>";

    // 创建TaskQueue实例
    $taskQueue = new TaskQueue($conn);

    // 准备测试数据
    $testData = [
        [
            'admin_account_name' => 'aishang',
            'account_id' => 553456211129746234,
            'project' => 8,
            'appid' => 'wxe3a874175a6e6ed3',
            'book_id' => 515208,
            'chapter_num' => 1,
            'name' => '完整修复测试A',
            'media_id' => 1,
            'postback_rule_id' => 6505,
            'recharge_panel_id' => 5501,
            'advertiser_account_id' => *********
        ],
        [
            'admin_account_name' => 'aishang',
            'account_id' => 553456211129746234,
            'project' => 8,
            'appid' => 'wxe3a874175a6e6ed3',
            'book_id' => 515208,
            'chapter_num' => 2,
            'name' => '完整修复测试B',
            'media_id' => 1,
            'postback_rule_id' => 6505,
            'recharge_panel_id' => 5501,
            'advertiser_account_id' => *********
        ]
    ];

    // 步骤1：创建任务
    echo "<h2>步骤1：创建任务</h2>";
    $taskId = $taskQueue->createTask($testData);
    echo "<p class='success'>✅ 任务创建成功，任务ID: $taskId</p>";

    // 验证数据是否正确插入
    $stmt = $conn->prepare("SELECT id, name, advertiser_account_id FROM create_promotion_link WHERE task_id = ?");
    $stmt->bind_param("s", $taskId);
    $stmt->execute();
    $result = $stmt->get_result();

    echo "<h3>插入的数据验证：</h3>";
    echo "<table>";
    echo "<tr><th>ID</th><th>链接名称</th><th>广告主账户ID</th></tr>";
    
    $insertedCorrectly = true;
    while ($row = $result->fetch_assoc()) {
        echo "<tr>";
        echo "<td>" . $row['id'] . "</td>";
        echo "<td>" . $row['name'] . "</td>";
        echo "<td>" . $row['advertiser_account_id'] . "</td>";
        echo "</tr>";
        
        // 检查名称是否正确
        if ($row['name'] === '0' || empty($row['name'])) {
            $insertedCorrectly = false;
        }
    }
    echo "</table>";

    if ($insertedCorrectly) {
        echo "<p class='success'>✅ 链接名称插入正确！</p>";
    } else {
        echo "<p class='error'>❌ 链接名称插入仍有问题</p>";
        exit;
    }

    // 步骤2：模拟成功的推广链接创建（直接插入到link表）
    echo "<h2>步骤2：模拟推广链接创建</h2>";
    
    $taskDetails = $taskQueue->getTaskDetails($taskId);
    foreach ($taskDetails as $detail) {
        // 模拟成功创建推广链接
        $stmt = $conn->prepare("
            INSERT INTO link (task_id, advertiser_account_id, app_name, appid, link, name)
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        
        $mockLink = "pages/index/index?id=496878&cnum=1&channel=rw-fx" . rand(100000, 999999);
        
        $stmt->bind_param("ssssss",
            $detail['task_id'],
            $detail['advertiser_account_id'],
            '七猫小说',
            $detail['appid'],
            $mockLink,
            $detail['name']
        );
        $stmt->execute();
        
        // 更新状态为完成
        $stmt = $conn->prepare("UPDATE create_promotion_link SET status = 'done', updated_at = NOW() WHERE id = ?");
        $stmt->bind_param("i", $detail['id']);
        $stmt->execute();
    }
    
    echo "<p class='success'>✅ 模拟推广链接创建完成</p>";

    // 步骤3：测试getTaskResults方法
    echo "<h2>步骤3：测试结果查询</h2>";
    $results = $taskQueue->getTaskResults($taskId);
    
    echo "<h3>查询到的结果：</h3>";
    echo "<table>";
    echo "<tr><th>链接名称</th><th>广告主账户ID</th><th>应用名称</th><th>推广链接</th></tr>";
    
    $resultsCorrect = true;
    foreach ($results as $result) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($result['link_name']) . "</td>";
        echo "<td>" . htmlspecialchars($result['advertiser_account_id']) . "</td>";
        echo "<td>" . htmlspecialchars($result['app_name']) . "</td>";
        echo "<td>" . htmlspecialchars($result['link']) . "</td>";
        echo "</tr>";
        
        // 检查链接名称是否正确
        if ($result['link_name'] === '0' || empty($result['link_name'])) {
            $resultsCorrect = false;
        }
    }
    echo "</table>";

    if ($resultsCorrect && count($results) > 0) {
        echo "<p class='success'>✅ 结果查询正确！链接名称显示正常</p>";
    } else {
        echo "<p class='error'>❌ 结果查询有问题</p>";
    }

    // 步骤4：测试Excel下载链接
    echo "<h2>步骤4：Excel下载测试</h2>";
    if (count($results) > 0) {
        echo "<p class='success'>✅ 可以生成Excel文件</p>";
        echo "<p><a href='task_status.php?task_id=$taskId&action=download' target='_blank'>下载Excel文件测试</a></p>";
    } else {
        echo "<p class='error'>❌ 没有结果数据，无法生成Excel</p>";
    }

    // 提供任务状态查询链接
    echo "<h2>相关链接</h2>";
    echo "<p><a href='task_status.php?task_id=$taskId' target='_blank'>查看任务状态页面</a></p>";

} catch (Exception $e) {
    echo "<p class='error'>❌ 错误: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "</body></html>";
?>
