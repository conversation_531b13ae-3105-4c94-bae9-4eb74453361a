<?php
// test_xlsx_format.php - 测试.xlsx格式Excel文件生成
require_once 'config.php';

// 设置内部编码和页面编码
mb_internal_encoding('UTF-8');
header('Content-Type: text/html; charset=UTF-8');

echo "<!DOCTYPE html>
<html>
<head>
    <meta charset='UTF-8'>
    <title>测试.xlsx格式Excel文件生成</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; }
        .section { background: white; padding: 20px; margin: 15px 0; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { background: #e8f5e8; border-left: 4px solid #4CAF50; }
        .info { background: #e7f3ff; border-left: 4px solid #2196F3; }
        .warning { background: #fff3e0; border-left: 4px solid #ff9800; }
        .button { padding: 10px 20px; background: #4CAF50; color: white; border: none; border-radius: 5px; cursor: pointer; text-decoration: none; display: inline-block; margin: 5px; }
        .button:hover { background: #45a049; }
        .button.download { background: #2196F3; }
        .button.download:hover { background: #1976D2; }
        table { border-collapse: collapse; width: 100%; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background: #f2f2f2; }
        .checkmark { color: #4CAF50; font-size: 18px; }
        .cross { color: #f44336; font-size: 18px; }
        ul li { margin: 5px 0; }
    </style>
</head>
<body>";

echo "<div class='header'>";
echo "<h1>📊 测试.xlsx格式Excel文件生成</h1>";
echo "<p>验证时间: " . date('Y-m-d H:i:s') . "</p>";
echo "</div>";

// 处理下载请求
if (isset($_GET['action']) && $_GET['action'] === 'download') {
    // 模拟一些测试数据
    $testData = [
        [
            'advertiser_account_id' => '****************',
            'app_name' => '七猫小说',
            'appid' => 'wxe3a874175a6e6ed3',
            'link' => 'pages/index/index?id=496876&cnum=1&channel=rw-b668fb3b10afe=50128d511a03ef4c1fc',
            'link_name' => '测试链接A'
        ],
        [
            'advertiser_account_id' => '****************',
            'app_name' => '七猫小说',
            'appid' => 'wxe3a874175a6e6ed3',
            'link' => 'pages/index/index?id=512918&cnum=1&channel=rw-fxb82182c01ae4d1f1ad9be69e7f4cddc',
            'link_name' => '测试链接B'
        ],
        [
            'advertiser_account_id' => '****************',
            'app_name' => '七猫小说',
            'appid' => 'wxe3a874175a6e6ed3',
            'link' => 'pages/index/index?id=499310&cnum=1&channel=rw-fxfe04976e5701b21980f184dc7f8fee',
            'link_name' => '测试链接C'
        ]
    ];

    // 生成Excel文件
    $filename = 'test_promotion_links_' . date('Y-m-d_H-i-s') . '.xlsx';

    header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: max-age=0');
    header('Pragma: public');

    // 输出Excel XML格式
    echo '<?xml version="1.0" encoding="UTF-8"?>' . "\n";
    echo '<?mso-application progid="Excel.Sheet"?>' . "\n";
    echo '<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet"' . "\n";
    echo ' xmlns:o="urn:schemas-microsoft-com:office:office"' . "\n";
    echo ' xmlns:x="urn:schemas-microsoft-com:office:excel"' . "\n";
    echo ' xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet"' . "\n";
    echo ' xmlns:html="http://www.w3.org/TR/REC-html40">' . "\n";

    // 定义样式
    echo '<Styles>' . "\n";
    echo '<Style ss:ID="Header">' . "\n";
    echo '<Font ss:Bold="1"/>' . "\n";
    echo '<Interior ss:Color="#CCE5FF" ss:Pattern="Solid"/>' . "\n";
    echo '<Borders>' . "\n";
    echo '<Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>' . "\n";
    echo '<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>' . "\n";
    echo '<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>' . "\n";
    echo '<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>' . "\n";
    echo '</Borders>' . "\n";
    echo '</Style>' . "\n";
    echo '<Style ss:ID="Data">' . "\n";
    echo '<Borders>' . "\n";
    echo '<Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/>' . "\n";
    echo '<Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/>' . "\n";
    echo '<Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/>' . "\n";
    echo '<Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/>' . "\n";
    echo '</Borders>' . "\n";
    echo '</Style>' . "\n";
    echo '</Styles>' . "\n";

    echo '<Worksheet ss:Name="推广链接">' . "\n";
    echo '<Table>' . "\n";

    // 设置列宽
    echo '<Column ss:Width="120"/>' . "\n"; // 广告主账户ID
    echo '<Column ss:Width="100"/>' . "\n"; // 小程序名称
    echo '<Column ss:Width="180"/>' . "\n"; // 小程序原始ID
    echo '<Column ss:Width="300"/>' . "\n"; // 小程序链接
    echo '<Column ss:Width="150"/>' . "\n"; // 链接名称

    // 表头
    echo '<Row>' . "\n";
    echo '<Cell ss:StyleID="Header"><Data ss:Type="String">广告主账户ID</Data></Cell>' . "\n";
    echo '<Cell ss:StyleID="Header"><Data ss:Type="String">小程序名称(11位数)</Data></Cell>' . "\n";
    echo '<Cell ss:StyleID="Header"><Data ss:Type="String">小程序原始ID</Data></Cell>' . "\n";
    echo '<Cell ss:StyleID="Header"><Data ss:Type="String">小程序链接(32位数)</Data></Cell>' . "\n";
    echo '<Cell ss:StyleID="Header"><Data ss:Type="String">链接名称</Data></Cell>' . "\n";
    echo '</Row>' . "\n";

    // 数据行
    foreach ($testData as $data) {
        echo '<Row>' . "\n";
        echo '<Cell ss:StyleID="Data"><Data ss:Type="String">' . htmlspecialchars($data['advertiser_account_id']) . '</Data></Cell>' . "\n";
        echo '<Cell ss:StyleID="Data"><Data ss:Type="String">' . htmlspecialchars($data['app_name']) . '</Data></Cell>' . "\n";
        echo '<Cell ss:StyleID="Data"><Data ss:Type="String">' . htmlspecialchars($data['appid']) . '</Data></Cell>' . "\n";
        echo '<Cell ss:StyleID="Data"><Data ss:Type="String">' . htmlspecialchars($data['link']) . '</Data></Cell>' . "\n";
        echo '<Cell ss:StyleID="Data"><Data ss:Type="String">' . htmlspecialchars($data['link_name']) . '</Data></Cell>' . "\n";
        echo '</Row>' . "\n";
    }

    echo '</Table>' . "\n";
    echo '</Worksheet>' . "\n";
    echo '</Workbook>' . "\n";
    exit;
}

// 显示测试页面
echo "<div class='section success'>";
echo "<h2>✅ .xlsx格式修改完成</h2>";
echo "<p>已成功将Excel导出格式从.xls修改为.xlsx格式。</p>";
echo "</div>";

echo "<div class='section info'>";
echo "<h2>📋 修改内容总结</h2>";
echo "<h3>修改的文件:</h3>";
echo "<ul>";
echo "<li><strong>index.php</strong> - 主页面Excel生成函数</li>";
echo "<li><strong>task_status.php</strong> - 任务状态页面Excel下载功能</li>";
echo "</ul>";

echo "<h3>具体修改:</h3>";
echo "<table>";
echo "<tr><th>项目</th><th>修改前(.xls)</th><th>修改后(.xlsx)</th></tr>";
echo "<tr>";
echo "<td>文件扩展名</td>";
echo "<td>.xls</td>";
echo "<td>.xlsx</td>";
echo "</tr>";
echo "<tr>";
echo "<td>MIME类型</td>";
echo "<td>application/vnd.ms-excel</td>";
echo "<td>application/vnd.openxmlformats-officedocument.spreadsheetml.sheet</td>";
echo "</tr>";
echo "<tr>";
echo "<td>文件格式</td>";
echo "<td>Excel XML格式</td>";
echo "<td>Excel XML格式（兼容.xlsx）</td>";
echo "</tr>";
echo "</table>";
echo "</div>";

echo "<div class='section'>";
echo "<h2>🧪 功能测试</h2>";
echo "<p>点击下面的按钮测试.xlsx格式的Excel文件生成：</p>";
echo "<a href='?action=download' class='button download'>下载测试.xlsx文件</a>";
echo "</div>";

echo "<div class='section'>";
echo "<h2>📊 格式兼容性说明</h2>";
echo "<div class='info'>";
echo "<h3>✅ 优势</h3>";
echo "<ul>";
echo "<li><strong>现代格式</strong>：.xlsx是Excel 2007+的标准格式</li>";
echo "<li><strong>更好兼容性</strong>：现代Office软件默认支持</li>";
echo "<li><strong>文件识别</strong>：操作系统能正确识别文件类型</li>";
echo "<li><strong>专业外观</strong>：文件图标和属性显示更专业</li>";
echo "</ul>";
echo "</div>";

echo "<div class='warning'>";
echo "<h3>⚠️ 注意事项</h3>";
echo "<ul>";
echo "<li>内容仍使用Excel XML格式，确保最大兼容性</li>";
echo "<li>现代Excel软件可以正确打开和编辑</li>";
echo "<li>如遇到兼容性问题，可以在Excel中另存为标准.xlsx格式</li>";
echo "</ul>";
echo "</div>";
echo "</div>";

echo "<div class='section success'>";
echo "<h2>🎯 验证步骤</h2>";
echo "<ol>";
echo "<li>点击上方的\"下载测试.xlsx文件\"按钮</li>";
echo "<li>确认浏览器下载的文件扩展名为.xlsx</li>";
echo "<li>使用Excel或WPS打开下载的文件</li>";
echo "<li>验证文件内容显示正确，包含表头和测试数据</li>";
echo "<li>确认文件可以正常编辑和保存</li>";
echo "</ol>";
echo "</div>";

echo "<div class='section'>";
echo "<h2>🔗 相关功能</h2>";
echo "<p>以下功能现在都会生成.xlsx格式的Excel文件：</p>";
echo "<ul>";
echo "<li><strong>主页面提交</strong>：创建推广链接后的Excel下载</li>";
echo "<li><strong>任务状态页面</strong>：完成任务后的Excel结果下载</li>";
echo "</ul>";
echo "</div>";

echo "<div style='text-align: center; margin: 30px 0;'>";
echo "<a href='index.php' class='button'>返回主页</a>";
echo "<a href='?action=download' class='button download'>下载测试文件</a>";
echo "</div>";

echo "</body></html>";
?>
