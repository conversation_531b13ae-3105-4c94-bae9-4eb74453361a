<?php
// task_queue_processor.php - 任务队列处理器
header('Content-Type: application/json; charset=utf-8');

require_once dirname(__DIR__) . '/config.php';
require_once dirname(__DIR__) . '/classes/TaskQueue.php';

// 初始化数据库连接
try {
    $conn = new mysqli(DB_HOST, DB_USER, DB_PASSWORD, DB_NAME);
    if ($conn->connect_error) {
        throw new Exception("数据库连接失败: " . $conn->connect_error);
    }
    $conn->set_charset("utf8mb4");
} catch (Exception $e) {
    die(json_encode([
        'code' => -1,
        'msg' => $e->getMessage(),
        'request_id' => uniqid('task_', true)
    ]));
}

// 初始化任务队列
$taskQueue = new TaskQueue($conn);

// 签名生成函数
function generate_signature($params, $secret_key) {
    $signParams = $params;
    unset($signParams['sign']);

    $filteredParams = array_filter($signParams, function($value) {
        return $value !== null && $value !== '';
    });

    ksort($filteredParams);

    $signStr = '';
    foreach ($filteredParams as $key => $value) {
        $signStr .= "{$key}={$value}&";
    }
    $signStr = rtrim($signStr, '&');

    $signStrWithSecret = $signStr . $secret_key;
    $sign = strtolower(md5($signStrWithSecret));

    return $sign;
}

// 创建单条推广链接
function create_single_promotion_link($taskData, $conn) {
    try {
        error_log("处理任务记录: " . $taskData['name']);

        // 设置API请求参数
        $params = [
            'admin_account_name' => $taskData['admin_account_name'],
            'account_id' => $taskData['account_id'],
            'project' => $taskData['project'],
            'appid' => $taskData['appid'],
            'book_id' => $taskData['book_id'],
            'chapter_num' => $taskData['chapter_num'],
            'name' => $taskData['name'],
            'media_id' => $taskData['media_id'],
            'postback_rule_id' => $taskData['postback_rule_id'],
            'recharge_panel_id' => $taskData['recharge_panel_id'],
            'access_key' => ACCESS_KEY,
            'random' => uniqid(),
            'timestamp' => time(),
            'is_platform_postback' => true
        ];

        // 生成签名
        $sign = generate_signature($params, SECRET_KEY);
        $params['sign'] = $sign;

        // 构造API请求URL
        $api_url = API_BASE_URL . '/mapi/v2/promotion-link/create';
        $timestamp = time();
        $full_api_url = $api_url . '?' . http_build_query($params) . '&timestamp=' . $timestamp . '&sign=' . $sign;

        error_log("发送POST请求: " . $full_api_url);

        // 发送POST请求
        $ch = curl_init($full_api_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($params));
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        // 解析响应
        $result = json_decode($response, true);

        if ($result === null || !isset($result['code'])) {
            throw new Exception("API请求失败，HTTP状态码: $httpCode，响应: " . substr($response, 0, 200));
        }

        if ($result['code'] !== 0) {
            if (isset($result['msg']) && strpos($result['msg'], '请求被限流') !== false) {
                throw new Exception("API频率限制: " . $result['msg']);
            }
            throw new Exception("API返回错误: " . ($result['msg'] ?? '未知错误'));
        }

        // 构建返回数据
        $linkData = [
            'id' => $result['data']['id'] ?? null,
            'link' => $result['data']['link'] ?? null,
            'ad_monitor_click_link' => $result['data']['ad_monitor_click_link'] ?? null
        ];

        // 查询app_name
        $stmt = $conn->prepare("SELECT app_name FROM recharge_templates WHERE appid = ? LIMIT 1");
        $stmt->bind_param("s", $taskData['appid']);
        $stmt->execute();
        $template_result = $stmt->get_result();
        $template_row = $template_result->fetch_assoc();

        $app_name = ($template_row && isset($template_row['app_name']))
            ? $template_row['app_name']
            : 'unknown_app';

        // 插入到link表，包含task_id
        if (!empty($linkData['link'])) {
            $stmt = $conn->prepare("
                INSERT INTO link (task_id, advertiser_account_id, app_name, appid, link, name)
                VALUES (?, ?, ?, ?, ?, ?)
            ");
            $stmt->bind_param("ssssss",
                $taskData['task_id'],
                $taskData['advertiser_account_id'],
                $app_name,
                $taskData['appid'],
                $linkData['link'],
                $taskData['name']
            );
            $stmt->execute();

            error_log("成功插入链接数据: 任务ID=" . $taskData['task_id'] . ", 链接=" . $linkData['link']);
        }

        return [
            'task_id' => $taskData['task_id'],
            'name' => $taskData['name'],
            'link_data' => $linkData,
            'status' => 'success'
        ];

    } catch (Exception $e) {
        error_log("处理任务记录失败: " . $e->getMessage());
        return [
            'task_id' => $taskData['task_id'],
            'name' => $taskData['name'],
            'error' => $e->getMessage(),
            'status' => 'failed'
        ];
    }
}

// 主处理逻辑
try {
    // 检查是否有正在运行的任务
    if ($taskQueue->hasRunningTask()) {
        echo json_encode([
            'code' => -2,
            'msg' => '有任务正在运行中，请稍后再试',
            'request_id' => uniqid('task_', true)
        ]);
        exit;
    }

    // 获取下一个待处理的任务
    $nextTaskId = $taskQueue->getNextPendingTask();

    if (!$nextTaskId) {
        echo json_encode([
            'code' => -3,
            'msg' => '没有待处理的任务',
            'request_id' => uniqid('task_', true)
        ]);
        exit;
    }

    error_log("开始处理任务: " . $nextTaskId);

    // 更新任务状态为运行中
    $taskQueue->updateTaskStatus($nextTaskId, 'running');

    // 获取任务详情
    $taskDetails = $taskQueue->getTaskDetails($nextTaskId);

    $processedCount = 0;
    $successCount = 0;
    $failedCount = 0;
    $results = [];
    $errors = [];
    $startTime = microtime(true);

    // 处理任务中的每条记录
    foreach ($taskDetails as $taskData) {
        $result = create_single_promotion_link($taskData, $conn);
        $processedCount++;

        if ($result['status'] === 'success') {
            $successCount++;
            $results[] = $result;

            // 更新单条记录状态为完成
            $stmt = $conn->prepare("UPDATE create_promotion_link SET status = 'done', updated_at = NOW() WHERE id = ?");
            $stmt->bind_param("i", $taskData['id']);
            $stmt->execute();
        } else {
            $failedCount++;
            $errors[] = $result;

            // 更新单条记录状态为失败
            $stmt = $conn->prepare("UPDATE create_promotion_link SET status = 'failed', error_message = ?, updated_at = NOW() WHERE id = ?");
            $stmt->bind_param("si", $result['error'], $taskData['id']);
            $stmt->execute();
        }

        // 如果不是最后一条记录，等待1.5秒
        if ($processedCount < count($taskDetails)) {
            error_log("等待1.5秒后处理下一条记录...");
            usleep(1500000); // 1.5秒间隔
        }
    }

    // 更新整个任务状态
    $finalStatus = ($failedCount === 0) ? 'done' : (($successCount === 0) ? 'failed' : 'done');
    $taskQueue->updateTaskStatus($nextTaskId, $finalStatus);

    $endTime = microtime(true);
    $totalTime = round($endTime - $startTime, 2);

    // 构建返回数据
    $return_data = [
        'code' => 0,
        'msg' => 'ok',
        'data' => [
            'task_id' => $nextTaskId,
            'total_records' => count($taskDetails),
            'processed' => $processedCount,
            'success' => $successCount,
            'failed' => $failedCount,
            'execution_time' => $totalTime,
            'results' => $results,
            'errors' => $errors
        ],
        'request_id' => uniqid('task_', true)
    ];

    error_log("任务处理完成: ID=$nextTaskId, 总数=" . count($taskDetails) . ", 成功=$successCount, 失败=$failedCount");

    echo json_encode($return_data, JSON_UNESCAPED_UNICODE);

} catch (Exception $e) {
    // 如果有任务ID，更新其状态为失败
    if (isset($nextTaskId)) {
        $taskQueue->updateTaskStatus($nextTaskId, 'failed', $e->getMessage());
    }

    error_log("任务处理错误: " . $e->getMessage());
    echo json_encode([
        'code' => -1,
        'msg' => '任务处理错误: ' . $e->getMessage(),
        'request_id' => uniqid('task_', true)
    ], JSON_UNESCAPED_UNICODE);
} finally {
    $conn->close();
}
?>
