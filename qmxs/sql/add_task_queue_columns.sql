-- 为 create_promotion_link 表添加任务队列相关字段
ALTER TABLE create_promotion_link
ADD COLUMN task_id VARCHAR(32) NOT NULL DEFAULT '' COMMENT '任务ID',
ADD COLUMN status ENUM('pending', 'running', 'done', 'failed') NOT NULL DEFAULT 'pending' COMMENT '任务状态',
ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
ADD COLUMN error_message TEXT NULL COMMENT '错误信息',
ADD INDEX idx_task_id (task_id),
ADD INDEX idx_status_created (status, created_at);

-- 为 link 表添加 task_id 关联字段
ALTER TABLE link
ADD COLUMN task_id VARCHAR(32) NOT NULL DEFAULT '' COMMENT '关联的任务ID',
ADD INDEX idx_task_id (task_id);
