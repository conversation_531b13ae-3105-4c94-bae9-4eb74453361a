<?php
require_once 'config.php';
require_once 'classes/TaskQueue.php';

echo "<!DOCTYPE html>";
echo "<html><head><meta charset='UTF-8'><title>手动任务处理测试</title>";
echo "<style>body{font-family:Arial,sans-serif;margin:20px;} .success{color:green;} .error{color:red;} .info{color:blue;} pre{background:#f5f5f5;padding:10px;border:1px solid #ddd;overflow-x:auto;}</style>";
echo "</head><body>";
echo "<h1>手动任务处理测试</h1>";

try {
    // 连接数据库
    $conn = new mysqli(DB_HOST, DB_USER, DB_PASSWORD, DB_NAME);
    if ($conn->connect_error) {
        throw new Exception("连接失败: " . $conn->connect_error);
    }
    $conn->set_charset("utf8mb4");

    // 创建TaskQueue实例
    $taskQueue = new TaskQueue($conn);

    // 查找等待中的任务
    $stmt = $conn->prepare("SELECT task_id, COUNT(*) as count FROM create_promotion_link WHERE status = 'pending' GROUP BY task_id ORDER BY MIN(created_at) ASC LIMIT 1");
    $stmt->execute();
    $result = $stmt->get_result();
    $pendingTask = $result->fetch_assoc();

    if ($pendingTask) {
        $taskId = $pendingTask['task_id'];
        echo "<p class='info'>找到等待中的任务: $taskId (共 {$pendingTask['count']} 条记录)</p>";

        // 检查是否有正在运行的任务
        if ($taskQueue->hasRunningTask()) {
            echo "<p class='error'>❌ 有任务正在运行中，请稍后再试</p>";
        } else {
            echo "<p class='success'>✅ 没有正在运行的任务，可以开始处理</p>";

            // 手动调用任务处理器
            echo "<h2>调用任务处理器</h2>";
            
            $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
            $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
            $baseUrl = $protocol . '://' . $host . dirname($_SERVER['REQUEST_URI']);
            $apiUrl = $baseUrl . '/modules/task_queue_processor.php';
            
            echo "<p><strong>API URL:</strong> $apiUrl</p>";
            
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $apiUrl);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 300); // 5分钟超时
            curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            
            echo "<p class='info'>开始处理任务，请稍候...</p>";
            echo "<script>document.body.style.cursor = 'wait';</script>";
            
            $startTime = microtime(true);
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            $curlError = curl_error($ch);
            curl_close($ch);
            $endTime = microtime(true);
            
            echo "<script>document.body.style.cursor = 'default';</script>";
            
            $executionTime = round($endTime - $startTime, 2);
            
            echo "<p><strong>执行时间:</strong> {$executionTime}秒</p>";
            echo "<p><strong>HTTP状态码:</strong> $httpCode</p>";
            
            if ($curlError) {
                echo "<p class='error'><strong>CURL错误:</strong> $curlError</p>";
            } else {
                echo "<h3>API响应:</h3>";
                echo "<pre>" . htmlspecialchars($response) . "</pre>";
                
                $result = json_decode($response, true);
                if ($result) {
                    echo "<h3>解析后的响应:</h3>";
                    if ($result['code'] === 0) {
                        echo "<p class='success'>✅ 任务处理成功</p>";
                        $data = $result['data'];
                        echo "<ul>";
                        echo "<li><strong>任务ID:</strong> " . $data['task_id'] . "</li>";
                        echo "<li><strong>总记录数:</strong> " . $data['total_records'] . "</li>";
                        echo "<li><strong>处理数量:</strong> " . $data['processed'] . "</li>";
                        echo "<li><strong>成功数量:</strong> " . $data['success'] . "</li>";
                        echo "<li><strong>失败数量:</strong> " . $data['failed'] . "</li>";
                        echo "<li><strong>执行时间:</strong> " . $data['execution_time'] . "秒</li>";
                        echo "</ul>";
                        
                        if ($data['failed'] > 0 && !empty($data['errors'])) {
                            echo "<h4>错误详情:</h4>";
                            echo "<pre>" . print_r($data['errors'], true) . "</pre>";
                        }
                    } else {
                        echo "<p class='error'>❌ 任务处理失败: " . ($result['msg'] ?? '未知错误') . "</p>";
                    }
                } else {
                    echo "<p class='error'>❌ 无法解析API响应</p>";
                }
            }

            // 检查任务状态
            echo "<h2>任务状态检查</h2>";
            $stmt = $conn->prepare("SELECT id, name, status, error_message FROM create_promotion_link WHERE task_id = ?");
            $stmt->bind_param("s", $taskId);
            $stmt->execute();
            $result = $stmt->get_result();
            
            echo "<table border='1' style='border-collapse:collapse; width:100%;'>";
            echo "<tr><th>ID</th><th>名称</th><th>状态</th><th>错误信息</th></tr>";
            while ($row = $result->fetch_assoc()) {
                $statusColor = '';
                switch($row['status']) {
                    case 'done': $statusColor = 'color:green;'; break;
                    case 'failed': $statusColor = 'color:red;'; break;
                    case 'running': $statusColor = 'color:blue;'; break;
                    case 'pending': $statusColor = 'color:orange;'; break;
                }
                
                echo "<tr>";
                echo "<td>" . $row['id'] . "</td>";
                echo "<td>" . htmlspecialchars($row['name']) . "</td>";
                echo "<td style='$statusColor'>" . $row['status'] . "</td>";
                echo "<td>" . htmlspecialchars($row['error_message'] ?? '') . "</td>";
                echo "</tr>";
            }
            echo "</table>";

            // 检查生成的链接
            echo "<h2>生成的链接检查</h2>";
            $stmt = $conn->prepare("SELECT name, link, advertiser_account_id FROM link WHERE task_id = ?");
            $stmt->bind_param("s", $taskId);
            $stmt->execute();
            $result = $stmt->get_result();
            
            if ($result->num_rows > 0) {
                echo "<table border='1' style='border-collapse:collapse; width:100%;'>";
                echo "<tr><th>链接名称</th><th>广告主账户ID</th><th>推广链接</th></tr>";
                while ($row = $result->fetch_assoc()) {
                    echo "<tr>";
                    echo "<td>" . htmlspecialchars($row['name']) . "</td>";
                    echo "<td>" . htmlspecialchars($row['advertiser_account_id']) . "</td>";
                    echo "<td style='word-break:break-all;'>" . htmlspecialchars($row['link']) . "</td>";
                    echo "</tr>";
                }
                echo "</table>";
                
                echo "<p class='success'>✅ 找到 " . $result->num_rows . " 条生成的链接</p>";
                echo "<p><a href='task_status.php?task_id=$taskId&action=download' target='_blank'>下载Excel文件</a></p>";
            } else {
                echo "<p class='info'>没有找到生成的链接</p>";
            }

            echo "<p><a href='task_status.php?task_id=$taskId' target='_blank'>查看任务状态页面</a></p>";
        }

    } else {
        echo "<p class='info'>没有找到等待中的任务</p>";
        
        // 显示所有任务状态
        echo "<h2>所有任务状态</h2>";
        $stmt = $conn->prepare("SELECT task_id, status, COUNT(*) as count, MIN(created_at) as created_at FROM create_promotion_link GROUP BY task_id, status ORDER BY created_at DESC LIMIT 10");
        $stmt->execute();
        $result = $stmt->get_result();
        
        echo "<table border='1' style='border-collapse:collapse; width:100%;'>";
        echo "<tr><th>任务ID</th><th>状态</th><th>记录数</th><th>创建时间</th></tr>";
        while ($row = $result->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($row['task_id']) . "</td>";
            echo "<td>" . $row['status'] . "</td>";
            echo "<td>" . $row['count'] . "</td>";
            echo "<td>" . $row['created_at'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }

} catch (Exception $e) {
    echo "<p class='error'>❌ 错误: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "</body></html>";
?>
